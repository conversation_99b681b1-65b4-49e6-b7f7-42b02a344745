#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
因子插件系统测试脚本

测试新的插件化因子系统的功能和性能
"""

import sys
import os
import pandas as pd
import numpy as np
import time
from datetime import datetime, timedelta

# 添加项目路径
sys.path.append('.')

from factors.plugin_system import get_plugin_manager
from factors.core.config_manager import get_config_manager
from utils.logger import get_logger

logger = get_logger('FactorPluginTest')


def generate_test_data(days: int = 100) -> pd.DataFrame:
    """生成测试数据"""
    dates = pd.date_range(start=datetime.now() - timedelta(days=days), periods=days, freq='D')
    
    # 生成模拟的OHLCV数据
    np.random.seed(42)
    base_price = 100.0
    
    data = []
    current_price = base_price
    
    for i, date in enumerate(dates):
        # 模拟价格随机游走
        change = np.random.normal(0, 0.02)
        current_price *= (1 + change)
        
        # 生成OHLC
        high = current_price * (1 + abs(np.random.normal(0, 0.01)))
        low = current_price * (1 - abs(np.random.normal(0, 0.01)))
        open_price = current_price * (1 + np.random.normal(0, 0.005))
        close = current_price
        
        # 生成成交量（偶尔有激增）
        base_volume = 1000000
        if np.random.random() < 0.1:  # 10%概率成交量激增
            volume = base_volume * np.random.uniform(3, 8)
        else:
            volume = base_volume * np.random.uniform(0.5, 2)
        
        data.append({
            'trade_time': date,
            'open': open_price,
            'high': high,
            'low': low,
            'close': close,
            'volume': int(volume)
        })
    
    return pd.DataFrame(data)


def test_plugin_discovery():
    """测试插件发现功能"""
    logger.info("🔍 测试插件发现功能...")
    
    plugin_manager = get_plugin_manager()
    
    # 获取统计信息
    stats = plugin_manager.get_statistics()
    logger.info(f"发现插件统计:")
    logger.info(f"  总插件数: {stats['total_plugins']}")
    logger.info(f"  启用插件数: {stats['enabled_plugins']}")
    logger.info(f"  已加载因子数: {stats['loaded_factors']}")
    logger.info(f"  可用场景: {', '.join(stats['available_scenarios'])}")
    
    # 列出所有插件
    plugins = plugin_manager.list_plugins()
    logger.info(f"\n📋 发现的插件列表:")
    for name, info in plugins.items():
        logger.info(f"  - {name} v{info.version} ({info.category})")
        logger.info(f"    描述: {info.description}")
        logger.info(f"    场景: {', '.join(info.scenarios)}")
        logger.info(f"    计算成本: {info.computational_cost}")
        logger.info(f"    优先级: {info.priority}")
        logger.info("")
    
    return len(plugins) > 0


def test_config_management():
    """测试配置管理功能"""
    logger.info("⚙️ 测试配置管理功能...")
    
    config_manager = get_config_manager()
    
    # 测试场景配置
    scenarios = ['intraday', 'after_market', 'research']
    for scenario in scenarios:
        config = config_manager.get_scenario_config(scenario)
        logger.info(f"场景 '{scenario}' 配置:")
        logger.info(f"  描述: {config.get('description', 'N/A')}")
        logger.info(f"  最大因子数: {config.get('max_factors', 'N/A')}")
        logger.info(f"  最大计算成本: {config.get('max_computational_cost', 'N/A')}")
    
    # 测试配置档案
    profiles = config_manager.list_profiles()
    logger.info(f"\n📁 可用配置档案: {', '.join(profiles)}")
    
    for profile_name in profiles:
        profile = config_manager.get_profile_info(profile_name)
        if profile:
            logger.info(f"档案 '{profile_name}':")
            logger.info(f"  描述: {profile.description}")
            logger.info(f"  场景: {profile.scenario}")
            logger.info(f"  因子数: {len(profile.factors)}")
    
    return True


def test_factor_loading():
    """测试因子加载功能"""
    logger.info("🔧 测试因子加载功能...")
    
    plugin_manager = get_plugin_manager()
    
    # 测试不同场景的因子加载
    scenarios = ['intraday', 'after_market']
    
    for scenario in scenarios:
        logger.info(f"\n测试场景: {scenario}")
        
        # 获取可用插件
        available_plugins = plugin_manager.get_available_plugins(scenario)
        logger.info(f"  可用插件数: {len(available_plugins)}")
        
        # 加载因子
        factors = plugin_manager.load_factors_for_scenario(scenario)
        logger.info(f"  成功加载因子数: {len(factors)}")
        
        for factor in factors:
            logger.info(f"    - {factor.factor_name} (类型: {factor.factor_type})")
    
    return True


def test_factor_calculation():
    """测试因子计算功能"""
    logger.info("🧮 测试因子计算功能...")
    
    # 生成测试数据
    test_data = generate_test_data(150)
    logger.info(f"生成测试数据: {len(test_data)} 行")
    
    plugin_manager = get_plugin_manager()
    
    # 为盘中监控场景加载因子
    factors = plugin_manager.load_factors_for_scenario('intraday')
    
    if not factors:
        logger.error("没有加载到任何因子")
        return False
    
    logger.info(f"测试 {len(factors)} 个因子的计算...")
    
    results = {}
    calculation_times = {}
    
    for factor in factors:
        try:
            start_time = time.time()
            
            # 计算因子
            result = factor.calculate_with_validation(test_data)
            
            end_time = time.time()
            calculation_time = end_time - start_time
            
            calculation_times[factor.factor_name] = calculation_time
            
            if result:
                results[factor.factor_name] = result
                logger.info(f"  ✅ {factor.factor_name}: {result.factor_value:.4f} "
                          f"(置信度: {result.confidence:.4f}, "
                          f"信号强度: {result.signal_strength}, "
                          f"耗时: {calculation_time:.3f}s)")
            else:
                logger.warning(f"  ❌ {factor.factor_name}: 计算失败")
                
        except Exception as e:
            logger.error(f"  💥 {factor.factor_name}: 计算异常 - {e}")
    
    # 性能统计
    if calculation_times:
        avg_time = np.mean(list(calculation_times.values()))
        max_time = max(calculation_times.values())
        min_time = min(calculation_times.values())
        
        logger.info(f"\n📊 性能统计:")
        logger.info(f"  平均计算时间: {avg_time:.3f}s")
        logger.info(f"  最大计算时间: {max_time:.3f}s")
        logger.info(f"  最小计算时间: {min_time:.3f}s")
        logger.info(f"  成功率: {len(results)}/{len(factors)} ({len(results)/len(factors)*100:.1f}%)")
    
    return len(results) > 0


def test_performance_benchmark():
    """性能基准测试"""
    logger.info("🏃 执行性能基准测试...")
    
    # 生成更大的测试数据集
    test_data = generate_test_data(300)
    
    plugin_manager = get_plugin_manager()
    factors = plugin_manager.load_factors_for_scenario('intraday')
    
    if not factors:
        logger.error("没有加载到任何因子")
        return False
    
    # 多次运行测试
    num_runs = 5
    all_times = []
    
    logger.info(f"执行 {num_runs} 次基准测试...")
    
    for run in range(num_runs):
        start_time = time.time()
        
        successful_calculations = 0
        for factor in factors:
            try:
                result = factor.calculate_with_validation(test_data)
                if result:
                    successful_calculations += 1
            except Exception:
                pass
        
        end_time = time.time()
        total_time = end_time - start_time
        all_times.append(total_time)
        
        logger.info(f"  第 {run+1} 次: {total_time:.3f}s "
                   f"(成功: {successful_calculations}/{len(factors)})")
    
    # 统计结果
    avg_time = np.mean(all_times)
    std_time = np.std(all_times)
    
    logger.info(f"\n🎯 基准测试结果:")
    logger.info(f"  平均总时间: {avg_time:.3f}s ± {std_time:.3f}s")
    logger.info(f"  平均每因子时间: {avg_time/len(factors):.3f}s")
    logger.info(f"  数据量: {len(test_data)} 行")
    logger.info(f"  因子数量: {len(factors)}")
    
    # 性能评级
    if avg_time < 1.0:
        grade = "🟢 优秀"
    elif avg_time < 3.0:
        grade = "🟡 良好"
    elif avg_time < 5.0:
        grade = "🟠 一般"
    else:
        grade = "🔴 需要优化"
    
    logger.info(f"  性能评级: {grade}")
    
    return True


def main():
    """主测试函数"""
    logger.info("🚀 开始因子插件系统测试")
    logger.info("=" * 60)
    
    tests = [
        ("插件发现", test_plugin_discovery),
        ("配置管理", test_config_management),
        ("因子加载", test_factor_loading),
        ("因子计算", test_factor_calculation),
        ("性能基准", test_performance_benchmark)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                logger.info(f"✅ {test_name} 测试通过")
                passed += 1
            else:
                logger.error(f"❌ {test_name} 测试失败")
        except Exception as e:
            logger.error(f"💥 {test_name} 测试异常: {e}")
            import traceback
            logger.error(traceback.format_exc())
    
    logger.info("\n" + "=" * 60)
    logger.info(f"🏁 测试完成: {passed}/{total} 通过")
    
    if passed == total:
        logger.info("🎉 所有测试通过！因子插件系统工作正常。")
        return True
    else:
        logger.error(f"⚠️ 有 {total - passed} 个测试失败，请检查系统配置。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
