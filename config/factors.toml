# 因子配置文件
# 版本: 2.0.0
# 格式: TOML

[defaults]
enabled = true
priority = "medium"
computational_cost = "medium"
cache_results = true
enable_validation = true
timeout = 10

# 因子分类配置
[categories.technical]
description = "技术指标类因子"

[categories.technical.max_factors_per_scenario]
intraday = 5
after_market = 10
research = -1
swing = 8
position = 6

[categories.volume]
description = "成交量类因子"

[categories.volume.max_factors_per_scenario]
intraday = 2
after_market = 5
research = -1
swing = 3
position = 2

[categories.composite]
description = "复合因子"

[categories.composite.max_factors_per_scenario]
intraday = 0  # 盘中不使用复合因子
after_market = 3
research = -1
swing = 2
position = 1

# 盘中监控核心因子配置
[factors.ema_factor]
enabled = true
priority = "high"
periods = [12, 62, 144, 169]
price_threshold = 0.005

[factors.bollinger_factor]
enabled = true
priority = "high"
period = 20
std_dev = 2.0
bbw_lookback = 126  # 盘中监控优化
squeeze_threshold = 0.2
expansion_threshold = 0.8
breakout_volume_ratio = 1.2
walking_days = 3
walking_tolerance = 0.02
trend_confirmation = false  # 盘中监控关闭以提高性能
output_mode = "boolean"

[factors.fibonacci_factor]
enabled = true
priority = "high"
lookback_period = 126  # 盘中监控优化
price_threshold = 0.005

[factors.volume_factor]
enabled = true
priority = "high"
lookback_period = 20
spike_threshold = 2.0
method = "mean"
percentile_threshold = 0.9

# 背离因子配置
[factors.macd_divergence_factor]
enabled = true
priority = "high"
fastperiod = 12
slowperiod = 26
signalperiod = 9
window = 5
min_bars_between = 15  # 盘中监控优化
output_mode = "boolean"

[factors.rsi_divergence_factor]
enabled = true
priority = "medium"
period = 14
window = 5
min_bars_between = 15
output_mode = "boolean"

[factors.kdj_divergence_factor]
enabled = true
priority = "medium"
k_period = 9
d_period = 3
j_period = 3
window = 5
min_bars_between = 15
output_mode = "boolean"

# 复合因子配置（仅盘后使用）
[factors.ema_turnaround_composite_factor]
enabled = false  # 默认禁用，仅在特定场景启用
priority = "medium"
ema_periods = [12, 62, 144]
fibonacci_lookback = 126
bollinger_period = 20
volume_lookback = 20
trend_weight = 0.4
support_resistance_weight = 0.4
volume_weight = 0.2

# 盘中监控场景特定配置覆盖
[scenarios.intraday.factors.ema_factor]
periods = [12, 62, 144, 169]
timeout = 3

[scenarios.intraday.factors.fibonacci_factor]
lookback_period = 126
fib_levels = [0.382, 0.5, 0.618]
timeout = 4

[scenarios.intraday.factors.bollinger_factor]
bbw_lookback = 126
trend_confirmation = false
timeout = 2

[scenarios.intraday.factors.volume_factor]
timeout = 2

[scenarios.intraday.factors.macd_divergence_factor]
min_bars_between = 15
timeout = 3

[scenarios.intraday.factors.rsi_divergence_factor]
timeout = 3

[scenarios.intraday.factors.kdj_divergence_factor]
timeout = 3

# 盘后分析场景启用复合因子
[scenarios.after_market.factors.ema_turnaround_composite_factor]
enabled = true

[scenarios.after_market.factors.bollinger_factor]
bbw_lookback = 252
trend_confirmation = true

# 研究场景启用所有因子和完整配置
[scenarios.research.factors.ema_turnaround_composite_factor]
enabled = true

[scenarios.research.factors.bollinger_factor]
bbw_lookback = 252
trend_confirmation = true

[scenarios.research.factors.macd_divergence_factor]
min_bars_between = 20

[scenarios.research.factors.rsi_divergence_factor]
min_bars_between = 20

[scenarios.research.factors.kdj_divergence_factor]
min_bars_between = 20
