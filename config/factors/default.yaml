# 因子系统默认配置文件
# 版本: 2.0.0
# 更新时间: 2025-08-31

# 全局设置
global_settings:
  enable_caching: true
  cache_size: 1000
  enable_optimization: true
  enable_qlib: false
  default_scenario: "intraday"
  performance_monitoring: true
  log_level: "INFO"
  
  # 性能设置
  max_concurrent_factors: 10
  factor_timeout: 30  # 秒
  memory_limit: "1GB"
  
  # 向量化计算设置
  enable_vectorization: true
  numpy_threads: 4
  use_numba: false  # 可选的JIT编译

# 因子默认设置
factor_defaults:
  enabled: true
  priority: "medium"
  computational_cost: "medium"
  cache_results: true
  enable_validation: true
  timeout: 10  # 秒
  
  # 数据验证设置
  min_data_points: 50
  max_missing_ratio: 0.1
  outlier_detection: true

# 场景配置
scenarios:
  # 盘中实时监控场景
  intraday:
    description: "盘中实时监控，要求低延迟和高可靠性"
    max_factors: 8
    max_computational_cost: "medium"
    enable_optimization: false
    timeout: 5
    priority_filter: ["high", "medium"]
    
    # 场景特定的因子配置
    factors:
      ema_factor:
        enabled: true
        periods: [12, 62, 144, 169]  # 减少周期数以提高性能
        price_threshold: 0.005
        
      fibonacci_factor:
        enabled: true
        lookback_period: 126  # 减少回看期
        price_threshold: 0.005
        
      bollinger_bands_factor:
        enabled: true
        period: 20
        std_dev: 2.0
        
      volume_spike_factor:
        enabled: true
        lookback_period: 20
        spike_threshold: 2.0
        
      macd_factor:
        enabled: true
        fast_period: 12
        slow_period: 26
        signal_period: 9
        
      # 禁用计算成本高的因子
      ema_turnaround_factor:
        enabled: false
      
      technical_composite_factor:
        enabled: false
      
      momentum_composite_factor:
        enabled: false

  # 盘后分析场景
  after_market:
    description: "盘后深度分析，可以使用更多计算资源"
    max_factors: 15
    max_computational_cost: "high"
    enable_optimization: true
    timeout: 30
    priority_filter: ["high", "medium", "low"]
    
    factors:
      ema_factor:
        enabled: true
        periods: [12, 62, 144, 169, 377, 576, 676]  # 完整周期
        
      ema_turnaround_factor:
        enabled: true
        target_period: 12
        lookback_days: 5
        
      fibonacci_factor:
        enabled: true
        lookback_period: 252
        
      bollinger_bands_factor:
        enabled: true
        
      volume_spike_factor:
        enabled: true
        
      price_volume_factor:
        enabled: true
        
      macd_factor:
        enabled: true
        
      technical_composite_factor:
        enabled: true
        trend_weight: 0.4
        support_resistance_weight: 0.4
        volume_weight: 0.2
        
      momentum_composite_factor:
        enabled: true

  # 研究分析场景
  research:
    description: "研究分析，启用所有因子和优化功能"
    max_factors: -1  # 无限制
    max_computational_cost: "high"
    enable_optimization: true
    timeout: 60
    priority_filter: ["high", "medium", "low"]
    
    factors:
      # 启用所有因子
      ema_factor:
        enabled: true
        
      ema_turnaround_factor:
        enabled: true
        
      fibonacci_factor:
        enabled: true
        
      bollinger_bands_factor:
        enabled: true
        
      volume_spike_factor:
        enabled: true
        
      price_volume_factor:
        enabled: true
        
      macd_factor:
        enabled: true
        
      technical_composite_factor:
        enabled: true
        
      momentum_composite_factor:
        enabled: true

# 因子分类配置
factor_categories:
  technical:
    description: "技术指标类因子"
    max_factors_per_scenario:
      intraday: 5
      after_market: 10
      research: -1
      
  volume:
    description: "成交量类因子"
    max_factors_per_scenario:
      intraday: 2
      after_market: 5
      research: -1
      
  composite:
    description: "复合因子"
    max_factors_per_scenario:
      intraday: 1
      after_market: 3
      research: -1

# 优化配置
optimization:
  enable_auto_optimization: false
  optimization_interval: "weekly"  # daily, weekly, monthly
  optimization_methods: ["grid_search", "differential_evolution"]
  
  # 参数优化范围
  parameter_ranges:
    price_threshold: [0.003, 0.005, 0.007, 0.01]
    lookback_period: [20, 50, 100, 200]
    spike_threshold: [1.5, 2.0, 2.5, 3.0]
    
  # 优化目标
  optimization_objectives:
    - "factor_value"
    - "confidence"
    - "signal_strength"

# 缓存配置
caching:
  enable_factor_cache: true
  cache_ttl: 300  # 秒
  max_cache_size: 1000
  cache_compression: true
  
  # 缓存策略
  cache_strategies:
    intraday: "memory"  # memory, redis, file
    after_market: "memory"
    research: "file"

# 监控和告警
monitoring:
  enable_performance_monitoring: true
  enable_error_tracking: true
  
  # 性能阈值
  performance_thresholds:
    max_calculation_time: 10  # 秒
    max_memory_usage: "500MB"
    min_success_rate: 0.95
    
  # 告警配置
  alerts:
    enable_email_alerts: false
    enable_log_alerts: true
    alert_levels: ["ERROR", "WARNING"]

# 数据质量配置
data_quality:
  enable_data_validation: true
  
  # 数据检查规则
  validation_rules:
    min_data_points: 50
    max_missing_ratio: 0.1
    price_range_check: true
    volume_range_check: true
    
  # 数据清洗
  data_cleaning:
    remove_outliers: true
    outlier_method: "iqr"  # iqr, zscore, isolation_forest
    fill_missing: true
    fill_method: "forward"  # forward, backward, interpolate

# 插件目录配置
plugin_directories:
  - "factors/technical"
  - "factors/volume" 
  - "factors/composite"
  - "factors/fundamental"  # 预留
  - "factors/sentiment"    # 预留
  - "factors/custom"       # 用户自定义

# 兼容性配置
compatibility:
  qlib_integration: false
  legacy_factor_support: true
  api_version: "2.0"
