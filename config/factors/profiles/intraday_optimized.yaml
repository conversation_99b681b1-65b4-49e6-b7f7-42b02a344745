# 盘中监控优化配置档案
# 专为盘中实时监控优化的因子配置
# 优先考虑性能和延迟

description: "盘中监控优化配置，专注于低延迟和高可靠性"
scenario: "intraday"

# 全局设置覆盖
global_settings:
  enable_caching: true
  cache_size: 500  # 减少缓存大小以节省内存
  enable_optimization: false  # 盘中不进行优化
  performance_monitoring: true
  max_concurrent_factors: 6  # 限制并发数
  factor_timeout: 5  # 更严格的超时

# 因子配置
factors:
  # 核心趋势因子 - 高优先级
  ema_factor:
    enabled: true
    priority: "high"
    periods: [12, 62, 144, 169]  # 只保留关键周期
    price_threshold: 0.005
    weight_decay: 0.1
    min_periods: 2  # 降低最小周期要求
    cache_results: true
    timeout: 3
    
  # 斐波那契因子 - 高优先级
  fibonacci_factor:
    enabled: true
    priority: "high"
    lookback_period: 126  # 减少回看期提高性能
    price_threshold: 0.005
    fib_levels: [0.382, 0.5, 0.618]  # 只保留关键位
    min_swing_size: 0.03  # 提高阈值过滤噪音
    cache_results: true
    timeout: 4
    
  # 布林带因子 - 中优先级
  bollinger_bands_factor:
    enabled: true
    priority: "medium"
    period: 20
    std_dev: 2.0
    squeeze_threshold: 0.2
    expansion_threshold: 0.8
    cache_results: true
    timeout: 2
    
  # 成交量激增因子 - 高优先级
  volume_spike_factor:
    enabled: true
    priority: "high"
    lookback_period: 20
    spike_threshold: 2.0
    method: "mean"  # 使用最快的方法
    cache_results: true
    timeout: 2
    
  # MACD因子 - 中优先级
  macd_factor:
    enabled: true
    priority: "medium"
    fast_period: 12
    slow_period: 26
    signal_period: 9
    divergence_lookback: 15  # 减少回看期
    cross_threshold: 0.0001
    cache_results: true
    timeout: 3
    
  # 禁用计算成本高的因子
  ema_turnaround_factor:
    enabled: false
    reason: "计算成本高，不适合盘中实时监控"
    
  price_volume_factor:
    enabled: false
    reason: "计算复杂，盘中监控中禁用"
    
  technical_composite_factor:
    enabled: false
    reason: "复合因子计算成本高，盘中监控中禁用"
    
  momentum_composite_factor:
    enabled: false
    reason: "复合因子计算成本高，盘中监控中禁用"

# 性能优化设置
performance_optimization:
  # 数据预处理优化
  enable_data_preprocessing: true
  batch_processing: false  # 盘中逐个处理
  
  # 计算优化
  enable_vectorization: true
  parallel_processing: false  # 避免线程开销
  
  # 内存优化
  memory_limit: "256MB"
  gc_frequency: "high"  # 频繁垃圾回收
  
  # 缓存优化
  cache_strategy: "memory"
  cache_ttl: 60  # 1分钟缓存
  preload_cache: true

# 数据质量设置（宽松以提高性能）
data_quality:
  min_data_points: 30  # 降低要求
  max_missing_ratio: 0.15  # 允许更多缺失
  outlier_detection: false  # 禁用异常值检测以提高速度
  
# 监控设置
monitoring:
  enable_performance_monitoring: true
  performance_thresholds:
    max_calculation_time: 3  # 更严格的时间要求
    max_memory_usage: "200MB"
    min_success_rate: 0.90  # 稍微降低成功率要求
    
  # 实时监控指标
  real_time_metrics:
    - "calculation_time"
    - "memory_usage"
    - "cache_hit_rate"
    - "factor_success_rate"

# 错误处理
error_handling:
  max_retries: 1  # 减少重试次数
  retry_delay: 0.1  # 快速重试
  fallback_strategy: "skip"  # 失败时跳过
  
# 日志设置
logging:
  log_level: "WARNING"  # 减少日志输出
  enable_debug: false
  log_performance: true
  log_errors: true
