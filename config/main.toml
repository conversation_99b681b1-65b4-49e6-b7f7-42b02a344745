[system]
log_level = "INFO"
timezone = "Asia/Shanghai"
trade_calendar_file = "data/trade_calendar.pkl"
data_dir = "data"
work_dir = "."

[processes]
market_data_fetcher = true
trade_signal_monitor = false
concept_monitor = false
news_monitor = false
risk_monitor = false
volume_ratio_analyzer = true
volume_surge_processor = true
intraday_stock_monitor = true

[process_schedule.market_data_fetcher]
start_time = "09:14:00"
stop_time = "15:20:00"

[process_schedule.volume_ratio_analyzer]
start_time = "09:33:00"
stop_time = "15:00:00"
depends_on = [
    "market_data_fetcher",
]

[process_schedule.after_market_scheduler]
execution_time = "16:00"

[process_schedule.volume_surge_processor]
start_time = "09:27:00"
stop_time = "15:05:00"
depends_on = [
    "market_data_fetcher",
]
priority = "HIGH"
auto_restart = true
max_restart_count = 3
restart_delay = 30

[process_schedule.intraday_stock_monitor]
start_time = "09:26:00"  # 保持09:26，确保market_data_fetcher有12分钟初始化时间
stop_time = "15:00:00"
depends_on = [
    "market_data_fetcher",
]
priority = "MEDIUM"
auto_restart = true
max_restart_count = 3
restart_delay = 30

[database]
default_type = "timescaledb"
use_timescaledb = true
host = "*********"
port = 6668
database = "xystock"
user = "postgres"
password = "241110"
connect_timeout = 10
client_encoding = "UTF8"
pool_size = 20
min_connections = 2
max_connections = 20
acquire_timeout = 10
max_lifetime = 1800
max_idle_time = 300
health_check_interval = 30
preload_pool = true
enable_monitoring = true
collect_stats = true
log_slow_queries = true
slow_query_threshold_ms = 500
enable_leak_detection = true
leak_detection_threshold = 120

[database.deadlock_retry]
max_retries = 3
base_delay = 0.1
max_delay = 2.0

[database.volume_surge_pool]
pool_size = 5
max_connections = 10
acquire_timeout = 5

[database_cache]
enable_cache = true
max_memory_size_mb = 100
default_ttl_seconds = 30
cleanup_interval_seconds = 60
batch_query_size = 100
enable_batch_optimization = true

[database_cache.strategies]
tick_data_ttl = 30
tick_data_priority = "HIGH"
kline_5min_ttl = 60
kline_5min_priority = "HIGH"
kline_day_ttl = 300
kline_day_priority = "NORMAL"
kline_2hour_ttl = 180
kline_2hour_priority = "NORMAL"
stock_info_ttl = 3600
stock_info_priority = "LOW"

[database_cache.warmup]
enable_warmup = true
warmup_stock_count = 100
warmup_on_startup = true
warmup_kline_days = 20
warmup_5min_periods = 50

[market]
trading_hours = [
    { start = "09:30:00", end = "11:30:00" },
    { start = "13:00:00", end = "15:00:00" },
]
stock_exchange = "SSE"
index_symbols = [
    "000001.SH",
    "399001.SZ",
    "399006.SZ",
]

[fetch]
tdx_server_ip = "auto"
tdx_enable_cache = true
update_interval = 3
batch_size = 80
retry_count = 3
fetch_daily_time = "15:30:00"
symbols_file = "config/stock_list.toml"

[notification]
enabled = false
notify_level = "WARNING"

[notification.email]
enabled = false
host = "smtp.example.com"
port = 587
user = "<EMAIL>"
password = "password"
recipients = [
    "<EMAIL>",
]

[notification.feishu]
enabled = true
webhook1 = "https://open.feishu.cn/open-apis/bot/v2/hook/9303bf07-0844-4df7-9d77-cd97006925c0"
secret1 = "Hs357aRJgyhu7XyS9r1fqb"
webhook2 = "https://open.feishu.cn/open-apis/bot/v2/hook/54d75029-f7d0-44c2-a742-09554fed28bf"
secret2 = "Utybfdf6V2j7kIA2XEuG2b"
webhook3 = "https://open.feishu.cn/open-apis/bot/v2/hook/e5023237-6768-4795-9328-fed854fb0645"
secret3 = "EoFu1JzYtbw8glbwFH1LWf"

[volume_ratio_analyzer]
enable = true
depends_on = [
    "market_data_fetcher",
]

[logging]
level = "DEBUG"
format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
file_path = "logs/stockfm.log"
max_size = 10
backup_count = 5

[market_data_fetcher]
use_database_cache = true

[performance_optimization]
enable_data_prefetch = true
prefetch_window_size = 100
enable_smart_cache = true
cache_hit_ratio_target = 0.9
max_concurrent_stocks = 50
enable_parallel_processing = true
gc_threshold_mb = 8192
enable_aggressive_gc = false
enable_memory_profiling = true
profiling_interval = 300
enable_performance_alerts = true

[strategies]
dual_channel_fibonacci_enabled = true

[strategies.dual_channel_fibonacci_config]
ema_short_1 = 144
ema_long_1 = 169
ema_short_2 = 576
ema_long_2 = 676
volume_window = 20
volume_ratio = 1.5
min_days = 5
max_days = 60
max_intrusion_days = 3
pre_check_days = 30
pivot_window = 5
weight_volume = 0.5
weight_amplitude = 0.2
weight_stability = 0.2
weight_structure = 0.1

# =====================================================
# 盘中选股监控配置
# =====================================================

[intraday_monitor]
# 基础配置
enabled = true
thread_count = 4
monitoring_interval = 1.0  # 每只股票的监控间隔（秒）
stock_refresh_interval = 300  # 股票列表刷新间隔（秒）

# 信号检测配置
[intraday_monitor.signal_detection]
price_threshold = 0.005  # 价格接近阈值（0.5%）
fibonacci_enabled = true
daily_ema_enabled = true
bollinger_bands_enabled = true
min_5_ema_enabled = true

# EMA周期配置
daily_ema_periods = [5, 10, 20, 30, 60]
min_5_ema_periods = [5, 10, 20, 30]

# 布林线配置
bollinger_period = 20
bollinger_std_dev = 2.0

# 缓存配置
[intraday_monitor.cache]
daily_kline_cache_ttl = 3600  # 日线数据缓存时间（秒）
indicator_cache_ttl = 1800    # 指标计算缓存时间（秒）
max_cache_size = 1000         # 最大缓存条目数

# 性能配置
[intraday_monitor.performance]
max_processing_time_per_stock = 5.0  # 单只股票最大处理时间（秒）
thread_health_check_interval = 30    # 线程健康检查间隔（秒）
error_threshold = 10                 # 错误阈值（超过则重启线程）

# 通知配置
[intraday_monitor.notification]
feishu_enabled = true
max_signals_per_minute = 20  # 每分钟最大信号数
signal_retry_count = 3       # 信号发送重试次数
signal_retry_delay = 5       # 重试延迟（秒）
