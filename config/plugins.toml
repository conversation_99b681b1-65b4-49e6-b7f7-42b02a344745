# 插件系统配置文件
# 版本: 2.0.0
# 格式: TOML

[system]
version = "2.0.0"
enable_caching = true
cache_size = 1000
enable_optimization = true
enable_qlib = false
default_scenario = "intraday"
performance_monitoring = true
log_level = "INFO"

[performance]
max_concurrent_factors = 10
factor_timeout = 30
memory_limit = "1GB"
enable_vectorization = true
numpy_threads = 4
use_numba = false

# 场景配置
[scenarios.intraday]
description = "盘中实时监控，要求低延迟和高可靠性"
max_factors = 8
max_computational_cost = "medium"
enable_optimization = false
timeout = 5
priority_filter = ["high", "medium"]

[scenarios.after_market]
description = "盘后深度分析，可以使用更多计算资源"
max_factors = 15
max_computational_cost = "high"
enable_optimization = true
timeout = 30
priority_filter = ["high", "medium", "low"]

[scenarios.research]
description = "研究分析，启用所有因子和优化功能"
max_factors = -1  # 无限制
max_computational_cost = "high"
enable_optimization = true
timeout = 60
priority_filter = ["high", "medium", "low"]

[scenarios.swing]
description = "波段交易分析"
max_factors = 12
max_computational_cost = "high"
enable_optimization = true
timeout = 20
priority_filter = ["high", "medium"]

[scenarios.position]
description = "持仓分析"
max_factors = 10
max_computational_cost = "medium"
enable_optimization = false
timeout = 15
priority_filter = ["high", "medium"]
