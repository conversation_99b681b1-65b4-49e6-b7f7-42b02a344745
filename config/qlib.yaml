# Qlib专用配置文件
# 版本: 2.0.0
# 格式: YAML (Qlib标准格式)

qlib:
  provider_uri: "file:///data/qlib"
  region: "cn"
  auto_mount: true
  flask_server: false
  logging_level: "INFO"
  
data:
  calendar_provider: "LocalCalendarProvider"
  instrument_provider: "LocalInstrumentProvider"
  feature_provider: "LocalFeatureProvider"
  
  # 数据路径配置
  calendar_path: "calendars"
  instrument_path: "instruments"
  feature_path: "features"
  
  # 数据更新配置
  auto_update: false
  update_frequency: "daily"

# Qlib兼容的因子配置
factors:
  # Qlib兼容因子列表
  qlib_compatible_factors:
    - "ema_factor"
    - "bollinger_bands_factor"
    - "fibonacci_factor"
    - "macd_factor"
    - "volume_spike_factor"
  
  # Qlib表达式因子
  expression_factors:
    close_price: "Close"
    volume: "Volume"
    returns: "Ref(Close, 0) / Ref(Close, 1) - 1"
    log_returns: "Log(Close / Ref(Close, 1))"
    
    # 技术指标表达式
    sma_5: "Mean(Close, 5)"
    sma_20: "Mean(Close, 20)"
    ema_12: "EMA(Close, 12)"
    ema_26: "EMA(Close, 26)"
    
    # 价格位置
    high_low_ratio: "(High - Low) / Close"
    close_position: "(Close - Low) / (High - Low)"
    
    # 成交量指标
    volume_ratio: "Volume / Mean(Volume, 20)"
    volume_price_trend: "Corr(Volume, Close, 10)"

# 模型配置
models:
  # 线性模型
  linear_model:
    class: "LinearModel"
    module_path: "qlib.contrib.model.linear"
    kwargs:
      estimator: "ridge"
      alpha: 0.05
  
  # LGBM模型
  lgb_model:
    class: "LGBModel"
    module_path: "qlib.contrib.model.gbdt"
    kwargs:
      loss: "mse"
      colsample_bytree: 0.8879
      learning_rate: 0.0421
      subsample: 0.8789
      lambda_l1: 205.6999
      lambda_l2: 580.9768
      max_depth: 8
      num_leaves: 210
      num_threads: 20

# 数据集配置
dataset:
  class: "DatasetH"
  module_path: "qlib.data.dataset"
  kwargs:
    handler:
      class: "Alpha158"
      module_path: "qlib.contrib.data.handler"
      kwargs:
        start_time: "2008-01-01"
        end_time: "2020-08-01"
        fit_start_time: "2008-01-01"
        fit_end_time: "2014-12-31"
        instruments: "csi300"
        infer_processors:
          - class: "RobustZScoreNorm"
            kwargs:
              fields_group: "feature"
              clip_outlier: true
          - class: "Fillna"
            kwargs:
              fields_group: "feature"
        learn_processors:
          - class: "DropnaLabel"
          - class: "CSRankNorm"
            kwargs:
              fields_group: "label"
        label: ["Ref($close, -2) / Ref($close, -1) - 1"]

# 策略配置
strategy:
  class: "TopkDropoutStrategy"
  module_path: "qlib.contrib.strategy.signal_strategy"
  kwargs:
    signal:
      - <MODEL>
      - <DATASET>
    topk: 50
    n_drop: 5

# 回测配置
backtest:
  start_time: "2017-01-01"
  end_time: "2020-08-01"
  account: *********
  benchmark: "SH000300"
  exchange_kwargs:
    limit_threshold: 0.095
    deal_price: "close"
    open_cost: 0.0005
    close_cost: 0.0015
    min_cost: 5

# 因子分析配置
factor_analysis:
  # IC分析
  ic_analysis:
    periods: [1, 5, 10, 20]
    method: "pearson"  # pearson, spearman
    
  # 分层回测
  layered_backtest:
    quantiles: 5
    periods: [1, 5, 10, 20]
    
  # 因子衰减分析
  decay_analysis:
    periods: [1, 2, 3, 5, 10, 20]
    
  # 换手率分析
  turnover_analysis:
    periods: [1, 5, 10, 20]

# 优化配置
optimization:
  # 超参数优化
  hyperparameter_optimization:
    method: "optuna"  # optuna, hyperopt, grid_search
    n_trials: 100
    timeout: 3600  # 秒
    
  # 因子选择
  factor_selection:
    method: "forward_selection"  # forward_selection, backward_elimination, rfe
    max_features: 50
    cv_folds: 5
