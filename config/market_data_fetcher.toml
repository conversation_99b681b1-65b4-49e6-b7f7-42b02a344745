[market_data_fetcher]
fetch_interval = 7
batch_size = 87
max_threads = 43
max_retries = 3
retry_delay = 0.5
timeout = 15
save_tick_to_db = true
save_kline_to_db = true
data_save_interval = 30
enable_kline_calculation = true
kline_periods = [ "5min", "1day",]
initialization_time = "09:05:00"
start_time = "09:28:00"
market_open_time = "09:30:00"
market_close_time = "15:01:00"
lunch_break_start = "11:31:00"
lunch_break_end = "13:00:00"
download_history_time = "16:30"
init_download_history = false
enable_post_market_download = false
enable_batch_optimization = true
adaptive_interval = true
performance_monitoring = true
max_processing_time = 5.6000000000000005

[logging]
level = "INFO"
format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
file_path = "logs/market_data_fetcher.log"
