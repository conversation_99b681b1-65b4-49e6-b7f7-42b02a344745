#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
K线生成功能测试脚本

用于测试修复后的K线生成逻辑是否正常工作
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from processes.volume_surge_processor import VolumeSurgeProcessor


def test_kline_generation():
    """测试K线生成功能"""
    print("🔍 开始测试K线生成功能...")
    
    try:
        # 创建处理器实例
        processor = VolumeSurgeProcessor()
        
        # 测试股票列表（少量股票用于测试）
        test_stocks = ['000001', '000002', '600000']
        
        print(f"📊 测试股票: {test_stocks}")
        
        # 测试K线生成
        print("🔄 开始生成K线数据...")
        success = processor._generate_and_save_klines_direct(
            stock_codes=test_stocks,
            periods=['5min', '15min']
        )
        
        if success:
            print("✅ K线生成测试成功")
            
            # 验证数据是否真的插入了
            print("🔍 验证插入的数据...")
            for stock_code in test_stocks:
                # 检查5分钟K线
                count_5min = processor.db_manager.fetch_one(
                    "SELECT COUNT(*) as count FROM stock_kline_5min WHERE stock_code = %s AND trade_time >= CURRENT_DATE",
                    (stock_code,)
                )
                
                # 检查15分钟K线
                count_15min = processor.db_manager.fetch_one(
                    "SELECT COUNT(*) as count FROM stock_kline_15min WHERE stock_code = %s AND trade_time >= CURRENT_DATE",
                    (stock_code,)
                )
                
                print(f"  {stock_code}: 5min={count_5min['count'] if count_5min else 0}条, 15min={count_15min['count'] if count_15min else 0}条")
        else:
            print("❌ K线生成测试失败")
            
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()


def test_batch_kline_retrieval():
    """测试批量K线数据获取"""
    print("\n🔍 开始测试批量K线数据获取...")
    
    try:
        processor = VolumeSurgeProcessor()
        test_stocks = ['000001', '000002', '600000']
        
        # 测试批量获取K线数据
        batch_klines = processor._get_batch_klines_timescale(test_stocks)
        
        print(f"📊 获取到的K线数据: {len(batch_klines)} 只股票")
        
        for stock_code, periods_data in batch_klines.items():
            print(f"  {stock_code}: {list(periods_data.keys())} 周期")
            for period, kline_data in periods_data.items():
                if kline_data:
                    print(f"    {period}: 成交量={kline_data.get('volume', 0)}, 收盘价={kline_data.get('close', 0)}")
                    
    except Exception as e:
        print(f"❌ 批量获取测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    print("🚀 K线生成功能测试开始")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 50)
    
    # 运行测试
    test_kline_generation()
    test_batch_kline_retrieval()
    
    print("=" * 50)
    print("✅ 测试完成")
