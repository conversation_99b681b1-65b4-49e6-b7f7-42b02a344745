#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
模块化盘中选股监控进程

使用新的因子框架重构的版本，展示如何使用模块化的因子系统

功能模块：
1. 股票列表管理 - 从stock_primary_signals表获取活跃股票
2. 多线程监控 - 4个工作线程并行处理
3. 数据库数据获取 - 复用market_data_fetcher.py的tick数据
4. 模块化因子计算 - 使用独立的因子模块
5. 信号检测 - 统一的信号检测框架
6. 信号去重 - 每个指标每天只发送一次
7. 飞书通知 - 发送卡片格式的信号通知

作者: QuantFM团队
创建时间: 2025-08-21
"""

import sys
import os
import time
import threading
import queue
from datetime import datetime, date
from typing import Dict, List, Optional, Any
import pandas as pd
import numpy as np

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入现有模块
from utils.logger import get_logger
from config.config_manager import get_config
from data.db_manager import get_db_manager
from services.feishu_notifier import FeishuNotifier

# 导入因子系统
from factors.base_factor import FactorManager

# 导入插件系统
try:
    from plugins import get_plugin_manager, get_config_manager
    PLUGIN_SYSTEM_AVAILABLE = True
except ImportError as e:
    print(f"警告: 插件系统不可用: {e}")
    PLUGIN_SYSTEM_AVAILABLE = False


class ModularIntradayStockMonitor:
    """模块化盘中选股监控主控制器"""
    
    def __init__(self):
        self.logger = get_logger('ModularIntradayStockMonitor')

        # 加载专用配置文件
        self.config = get_config(config_file="config/intraday_stock_monitor.toml")
        self.main_config = get_config()  # 主配置文件
        self.db_manager = get_db_manager()

        # 配置参数
        monitor_config = self.config.get('monitor', {})
        self.thread_count = monitor_config.get('thread_count', 4)
        self.monitoring_interval = monitor_config.get('monitoring_interval', 1.0)
        self.stock_refresh_interval = monitor_config.get('stock_refresh_interval', 300)
        self.price_threshold = self.config.get('signal_detection', {}).get('price_threshold', 0.005)
        
        # 核心组件
        self.feishu_notifier = None
        
        # 插件化因子管理器
        if PLUGIN_SYSTEM_AVAILABLE:
            try:
                self.plugin_manager = get_plugin_manager()
                self.config_manager = get_config_manager()
                self.factor_manager = FactorManager()
                self._init_factors_with_plugins()
                self.logger.info("✅ 使用插件系统加载因子")
            except Exception as e:
                self.logger.error(f"插件系统初始化失败: {e}")
                self.logger.info("🔄 降级到直接因子加载")
                self.factor_manager = FactorManager()
                self._init_factors_direct()
        else:
            self.logger.info("🔄 使用直接因子加载")
            self.factor_manager = FactorManager()
            self._init_factors_direct()
        
        # 线程管理
        self.worker_threads = []
        self.task_queue = queue.Queue()  # 修正变量名
        self.running = False

        # 信号去重缓存
        self.sent_signals = set()  # 修正为set类型

        # 数据缓存（历史数据在交易日内固定，可以缓存）
        self._data_cache = {}
        self._cache_date = None

        # 性能统计
        self.stats = {
            'stocks_processed': 0,  # 修正变量名
            'signals_detected': 0,
            'signals_sent': 0,
            'factors_calculated': 0,  # 新增
            'errors': 0
        }
        
        self.logger.info("🚀 模块化盘中选股监控进程初始化完成")
    
    def _init_factors_with_plugins(self):
        """使用插件系统初始化因子"""
        try:
            # 使用优化的盘中监控配置档案
            scenario = "intraday"

            # 获取因子配置
            factor_configs = {}

            # 从配置管理器获取场景配置
            scenario_config = self.config_manager.get_scenario_config(scenario)
            if scenario_config:
                factor_configs = scenario_config.factors
                self.logger.info(f"使用场景配置: {scenario}")
            else:
                self.logger.warning(f"未找到场景配置: {scenario}")

            # 使用插件管理器为盘中监控场景加载因子
            factors = self.plugin_manager.load_factors_for_scenario(
                scenario=scenario,
                factor_configs=factor_configs
            )

            # 注册加载的因子到因子管理器
            for factor in factors:
                self.factor_manager.register_factor(factor)

            # 输出详细统计信息
            plugin_stats = self.plugin_manager.get_statistics()
            self.logger.info(f"✅ 插件化因子系统初始化完成:")
            self.logger.info(f"   总插件数: {plugin_stats['total_plugins']}")
            self.logger.info(f"   启用插件数: {plugin_stats['enabled_plugins']}")
            self.logger.info(f"   为盘中监控加载因子数: {len(factors)}")
            self.logger.info(f"   可用场景: {', '.join(plugin_stats['available_scenarios'])}")

            if factors:
                factor_names = [f.factor_name for f in factors]
                self.logger.info(f"   加载的因子: {', '.join(factor_names)}")

                # 输出因子性能信息
                for factor in factors:
                    factor_info = f"     - {factor.factor_name}: "
                    factor_info += f"数据长度≥{factor.min_data_length}, "
                    factor_info += f"列要求{factor.required_columns}"
                    self.logger.debug(factor_info)
            else:
                self.logger.warning("⚠️ 没有加载任何因子，请检查配置")

        except Exception as e:
            self.logger.error(f"插件化因子初始化失败: {e}")
            import traceback
            self.logger.error(f"详细错误: {traceback.format_exc()}")
            # 降级到直接加载
            self._init_factors_direct()

    def _init_factors_direct(self):
        """直接初始化因子（降级方案）"""
        try:
            # 直接导入和注册因子
            from factors.ema_factor import EmaFactor
            from factors.bollinger_factor import BollingerFactor
            from factors.fibonacci_factor import FibonacciFactor
            from factors.divergence.macd_divergence import MACDDivergenceFactor
            from factors.divergence.rsi_divergence import RSIDivergenceFactor
            from factors.divergence.kdj_divergence import KDJDivergenceFactor

            # 创建因子实例（使用盘中优化配置）
            factors = [
                EmaFactor(periods=[12, 62, 144, 169], price_threshold=0.005),
                BollingerFactor(period=20, std_dev=2.0),
                FibonacciFactor(lookback_period=252, price_threshold=0.005),
                MACDDivergenceFactor(fastperiod=12, slowperiod=26, signalperiod=9),
                RSIDivergenceFactor(period=14, window=5),
                KDJDivergenceFactor(fastk_period=9, slowk_period=3, slowd_period=3)
            ]

            # 注册因子到因子管理器
            for factor in factors:
                self.factor_manager.register_factor(factor)

            self.logger.info(f"✅ 已注册 {len(factors)} 个因子")

            if factors:
                factor_names = [f.factor_name for f in factors]
                self.logger.info(f"   加载的因子: {', '.join(factor_names)}")
            else:
                self.logger.warning("⚠️ 没有加载任何因子，请检查配置")

        except Exception as e:
            self.logger.error(f"初始化因子失败: {e}")
            import traceback
            self.logger.error(f"详细错误: {traceback.format_exc()}")
            # 降级到空因子管理器，避免程序崩溃
            self.factor_manager = FactorManager()
    
    def start(self):
        """启动监控进程"""
        try:
            self.logger.info("📊 启动模块化盘中选股监控进程...")
            self.running = True
            
            # 1. 使用数据库模式
            self.logger.info("✅ 使用数据库模式获取数据")

            # 2. 初始化飞书通知器
            feishu_success = self._init_feishu_notifier()
            if not feishu_success:
                self.logger.warning("⚠️ 飞书通知器初始化失败")
                self.feishu_notifier = None
            else:
                self.logger.info("✅ 飞书通知器初始化成功")
            
            # 3. 获取活跃股票列表
            active_stocks = self._get_active_stocks()
            if not active_stocks:
                self.logger.error("❌ 未获取到活跃股票列表，监控进程退出")
                return
            
            self.logger.info(f"📈 获取到 {len(active_stocks)} 只活跃股票")
            
            # 4. 启动工作线程
            self._start_worker_threads()
            
            # 5. 主循环
            self._main_monitoring_loop(active_stocks)
            
        except Exception as e:
            self.logger.error(f"❌ 启动监控进程失败: {e}")
        finally:
            self.stop()
    
    def _process_stock(self, stock_info: Dict):
        """处理单只股票（使用新的因子框架）"""
        try:
            stock_code = stock_info['stock_code']
            
            # 1. 获取当前价格
            current_price = self._get_current_price(stock_code)
            if current_price is None:
                return
            
            # 2. 获取历史数据
            df = self._get_stock_historical_data(stock_code)
            if df is None or len(df) < 50:
                return
            
            # 3. 使用因子管理器计算所有因子
            factor_results = self.factor_manager.calculate_all_factors(
                df,
                stock_code=stock_code,
                current_price=current_price,
                start_low_price=stock_info.get('start_low_price', None)
            )
            
            # 4. 处理因子结果，生成信号
            signals = self._convert_factors_to_signals(
                factor_results, stock_info, current_price
            )
            
            # 5. 处理检测到的信号
            for signal in signals:
                self._handle_signal(signal, stock_info)
            
            self.stats['stocks_processed'] += 1
            
        except Exception as e:
            self.logger.error(f"❌ 处理股票 {stock_info.get('stock_code', '')} 失败: {e}")
            self.stats['errors'] += 1
    
    def _convert_factors_to_signals(self, factor_results: Dict, stock_info: Dict, 
                                  current_price: float) -> List[Dict]:
        """将因子结果转换为信号"""
        signals = []
        
        try:
            stock_code = stock_info['stock_code']
            stock_name = stock_info.get('stock_name', stock_code)
            
            for factor_name, result in factor_results.items():
                # 只处理有效信号
                if result.signal_strength in ['WEAK', 'MEDIUM', 'STRONG']:
                    
                    # 确定指标值（用于显示）
                    indicator_value = current_price
                    if result.metadata:
                        # 尝试从元数据中获取更合适的指标值
                        if 'best_level' in result.metadata:
                            indicator_value = result.metadata['best_level'].get('value', current_price)
                        elif 'best_oscillation_value' in result.metadata:
                            indicator_value = result.metadata['best_oscillation_value']
                        elif 'ema_value' in result.metadata:
                            indicator_value = result.metadata['ema_value']
                        elif 'bb_values' in result.metadata and result.metadata['near_bands']:
                            indicator_value = result.metadata['near_bands'][0]['value']
                    
                    signal = {
                        'stock_code': stock_code,
                        'stock_name': stock_name,
                        'signal_type': result.factor_type,
                        'indicator_name': factor_name,
                        'current_price': current_price,
                        'indicator_value': indicator_value,
                        'deviation': abs(current_price - indicator_value) / current_price,
                        'signal_time': datetime.now(),
                        'factor_value': result.factor_value,
                        'signal_strength': result.signal_strength,
                        'confidence': result.confidence,
                        'metadata': result.metadata
                    }
                    
                    signals.append(signal)
            
            return signals
            
        except Exception as e:
            self.logger.error(f"转换因子结果为信号失败: {e}")
            return []
    
    def _get_stock_historical_data(self, stock_code: str) -> Optional[pd.DataFrame]:
        """获取股票历史数据"""
        try:
            # 获取日线数据
            query = """
            SELECT trade_time, open, high, low, close, volume
            FROM stock_kline_day 
            WHERE stock_code = %s 
            ORDER BY trade_time DESC 
            LIMIT 1700
            """
            query_tick = """
            SELECT trade_time, open, high, low, price, volume
            FROM stock_tick_data
            WHERE stock_code = %s
            AND DATE(trade_time) = CURRENT_DATE
            ORDER BY trade_time DESC
            LIMIT 1
            """
            
            # 获取历史日线数据（可缓存）
            historical_data = self._get_cached_historical_data(stock_code, query)
            if historical_data is None:
                return None

            # 获取最新tick数据
            current_tick = self._get_current_tick_data(stock_code, query_tick)

            # 合并数据
            complete_data = self._merge_historical_and_current_data(historical_data, current_tick)

            return complete_data
            
        except Exception as e:
            self.logger.error(f"获取股票 {stock_code} 历史数据失败: {e}")
            return None

    def _get_cached_historical_data(self, stock_code: str, query: str) -> Optional[pd.DataFrame]:
        """获取缓存的历史数据"""
        try:
            # 生成缓存键（基于交易日）
            today = datetime.now().strftime('%Y-%m-%d')
            cache_key = f"historical_data_{stock_code}_{today}"

            # 检查缓存
            if hasattr(self, '_data_cache') and cache_key in self._data_cache:
                self.logger.debug(f"使用缓存的历史数据: {stock_code}")
                return self._data_cache[cache_key]

            # 从数据库获取历史数据
            result = self.db_manager.fetch_all(query, [stock_code])
            if not result:
                return None

            # 转换为DataFrame
            df = pd.DataFrame(result, columns=['trade_time', 'open', 'high', 'low', 'close', 'volume'])
            df = df.sort_values('trade_time').reset_index(drop=True)
            df['trade_time'] = pd.to_datetime(df['trade_time'])

            for col in ['open', 'high', 'low', 'close', 'volume']:
                df[col] = pd.to_numeric(df[col])

            # 缓存数据
            if not hasattr(self, '_data_cache'):
                self._data_cache = {}
            self._data_cache[cache_key] = df.copy()

            self.logger.info(f"缓存历史数据: {stock_code}, 数据量: {len(df)} 条")
            return df

        except Exception as e:
            self.logger.error(f"获取缓存历史数据失败 {stock_code}: {e}")
            return None

    def _get_current_tick_data(self, stock_code: str, query_tick: str) -> Optional[Dict]:
        """获取当前最新tick数据"""
        try:
            result = self.db_manager.fetch_one(query_tick, [stock_code])
            if not result:
                self.logger.warning(f"未找到股票 {stock_code} 的最新tick数据")
                return None

            # 解析tick数据
            tick_data = {
                'trade_time': result[0],
                'open': float(result[1]),
                'high': float(result[2]),
                'low': float(result[3]),
                'price': float(result[4]),  # 当前价格
                'volume': int(result[5])
            }

            self.logger.debug(f"获取最新tick数据: {stock_code}, 价格: {tick_data['price']}")
            return tick_data

        except Exception as e:
            self.logger.error(f"获取最新tick数据失败 {stock_code}: {e}")
            return None

    def _merge_historical_and_current_data(self, historical_data: pd.DataFrame,
                                         current_tick: Optional[Dict]) -> pd.DataFrame:
        """合并历史数据和当前tick数据"""
        try:
            # 如果没有当前tick数据，直接返回历史数据
            if current_tick is None:
                self.logger.warning("没有当前tick数据，使用历史数据")
                return historical_data

            # 格式化当前tick的交易时间为0点格式
            current_time = pd.to_datetime(current_tick['trade_time'])
            current_date = current_time.strftime('%Y-%m-%d')
            formatted_time = pd.to_datetime(f"{current_date} 00:00:00")

            # 检查今日是否已有数据
            today_data = historical_data[historical_data['trade_time'].dt.date == current_time.date()]

            if len(today_data) > 0:
                # 更新今日数据
                today_idx = historical_data[historical_data['trade_time'].dt.date == current_time.date()].index[-1]

                # 更新当日的OHLC数据
                # 开盘价保持不变，收盘价更新为当前价格
                historical_data.loc[today_idx, 'close'] = current_tick['price']

                # 更新最高价和最低价（如果当前价格更极端）
                if current_tick['price'] > historical_data.loc[today_idx, 'high']:
                    historical_data.loc[today_idx, 'high'] = current_tick['price']
                if current_tick['price'] < historical_data.loc[today_idx, 'low']:
                    historical_data.loc[today_idx, 'low'] = current_tick['price']

                # 更新成交量（累计）
                historical_data.loc[today_idx, 'volume'] = current_tick['volume']

                self.logger.debug(f"更新今日数据: 收盘价 {current_tick['price']}")

            else:
                # 添加新的今日数据
                new_row = {
                    'trade_time': formatted_time,
                    'open': current_tick['open'],
                    'high': current_tick['high'],
                    'low': current_tick['low'],
                    'close': current_tick['price'],
                    'volume': current_tick['volume']
                }

                # 添加到DataFrame
                new_df = pd.DataFrame([new_row])
                historical_data = pd.concat([historical_data, new_df], ignore_index=True)
                historical_data = historical_data.sort_values('trade_time').reset_index(drop=True)

                self.logger.info(f"添加今日数据: 开盘 {current_tick['open']}, 当前 {current_tick['price']}")

            return historical_data

        except Exception as e:
            self.logger.error(f"合并历史和当前数据失败: {e}")
            return historical_data

    def _clear_cache_if_new_day(self):
        """如果是新的交易日，清理缓存"""
        try:
            current_date = datetime.now().strftime('%Y-%m-%d')

            if self._cache_date != current_date:
                self.logger.info(f"检测到新交易日 {current_date}，清理历史数据缓存")
                self._data_cache.clear()
                self._cache_date = current_date

        except Exception as e:
            self.logger.error(f"清理缓存失败: {e}")

    def _get_cache_statistics(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        try:
            cache_size = len(self._data_cache)
            cache_memory = 0

            for key, df in self._data_cache.items():
                if isinstance(df, pd.DataFrame):
                    cache_memory += df.memory_usage(deep=True).sum()

            return {
                'cache_entries': cache_size,
                'cache_memory_mb': cache_memory / (1024 * 1024),
                'cache_date': self._cache_date
            }

        except Exception as e:
            self.logger.error(f"获取缓存统计失败: {e}")
            return {}
    
    # 其他方法保持与原版本相似，但使用新的因子框架
    # 这里省略了一些辅助方法的实现，重点展示因子框架的使用
    

    
    def _init_feishu_notifier(self) -> bool:
        """初始化飞书通知器"""
        try:
            # 从主配置文件获取飞书配置
            feishu_config = self.main_config.get('notification', {}).get('feishu', {})

            if not feishu_config:
                self.logger.warning("⚠️ 未找到飞书配置")
                return False

            # 获取webhook和secret
            webhook_url = feishu_config.get('webhook2', '')
            secret = feishu_config.get('secret2', '')

            if not webhook_url:
                self.logger.warning("⚠️ 飞书webhook_url未配置")
                return False

            # 创建飞书通知器
            self.feishu_notifier = FeishuNotifier(
                webhook_url=webhook_url,
                secret=secret,
                logger=self.logger,
                use_signature=True
            )

            self.logger.info("✅ 飞书通知器初始化成功")
            return True

        except Exception as e:
            self.logger.error(f"❌ 初始化飞书通知器失败: {e}")
            return False
    
    def _get_active_stocks(self) -> List[Dict]:
        """获取活跃股票列表"""
        try:
            # 从stock_primary_signals表获取活跃股票
            query = """
            SELECT DISTINCT
                sps.stock_code,
                si.stock_name as stock_name,
                sps.start_low_price,
                sps.created_at
            FROM stock_primary_signals sps
            LEFT JOIN stock_info si ON sps.stock_code = si.stock_code
            WHERE sps.is_active = true
            ORDER BY sps.created_at DESC
            """

            results = self.db_manager.fetch_all(query)

            if not results:
                self.logger.warning("⚠️ 未找到活跃股票")
                return []

            active_stocks = []
            for row in results:
                stock_info = {
                    'stock_code': row['stock_code'],
                    'stock_name': row['stock_name'] or row['stock_code'],
                    'start_low_price': float(row['start_low_price']) if row['start_low_price'] else None,
                    'created_at': row['created_at']
                }
                active_stocks.append(stock_info)

            self.logger.info(f"📈 获取到 {len(active_stocks)} 只活跃股票")
            return active_stocks

        except Exception as e:
            self.logger.error(f"❌ 获取活跃股票列表失败: {e}")
            return []
    
    def _get_current_price(self, stock_code: str) -> Optional[float]:
        """获取当前价格"""
        try:
            # 从最新的tick数据获取当前价格
            query = """
            SELECT price
            FROM stock_tick_data
            WHERE stock_code = %s
            AND DATE(trade_time) = CURRENT_DATE
            ORDER BY trade_time DESC
            LIMIT 1
            """

            result = self.db_manager.fetch_one(query, [stock_code])

            if result:
                return float(result['price'])
            else:
                # 如果没有今日tick数据，尝试获取最新的日线收盘价
                query_daily = """
                SELECT close
                FROM stock_kline_day
                WHERE stock_code = %s
                ORDER BY trade_time DESC
                LIMIT 1
                """

                result_daily = self.db_manager.fetch_one(query_daily, [stock_code])
                if result_daily:
                    return float(result_daily['close'])

            return None

        except Exception as e:
            self.logger.error(f"❌ 获取股票 {stock_code} 当前价格失败: {e}")
            return None

    def _is_trading_time(self) -> bool:
        """判断是否为交易时间"""
        try:
            from utils.time import TradeCalendar

            trade_calendar = TradeCalendar()

            # 检查是否为交易日
            if not trade_calendar.is_trade_date():
                return False

            # 检查是否在交易时间内
            now = datetime.now().time()

            # 上午交易时间: 09:30-11:30
            morning_start = datetime.strptime("09:30:00", "%H:%M:%S").time()
            morning_end = datetime.strptime("11:30:00", "%H:%M:%S").time()

            # 下午交易时间: 13:00-15:00
            afternoon_start = datetime.strptime("13:00:00", "%H:%M:%S").time()
            afternoon_end = datetime.strptime("15:00:00", "%H:%M:%S").time()

            # 判断是否在交易时间内
            is_morning_trading = morning_start <= now <= morning_end
            is_afternoon_trading = afternoon_start <= now <= afternoon_end

            return is_morning_trading or is_afternoon_trading

        except Exception as e:
            self.logger.error(f"❌ 判断交易时间失败: {e}")
            return False


    
    def _handle_signal(self, signal: Dict, stock_info: Dict):
        """处理信号"""
        try:
            # 检查信号去重
            signal_key = f"{stock_info['stock_code']}_{signal['signal_type']}_{date.today()}"

            if signal_key in self.sent_signals:
                self.logger.debug(f"信号已发送过，跳过: {signal_key}")
                return

            # 发送飞书通知
            if self.feishu_notifier:
                self._send_signal_notification(signal, stock_info)

            # 记录已发送的信号
            self.sent_signals.add(signal_key)

            # 更新统计
            self.stats['signals_sent'] += 1

            self.logger.info(f"✅ 信号处理完成: {stock_info['stock_code']} - {signal['signal_type']}")

        except Exception as e:
            self.logger.error(f"❌ 处理信号失败: {e}")

    def _send_signal_notification(self, signal: Dict, stock_info: Dict):
        """发送信号通知"""
        try:
            # 构造通知数据
            notification_data = {
                'stock_code': stock_info['stock_code'],
                'stock_name': stock_info['stock_name'],
                'signal_type': signal['signal_type'],
                'current_price': signal.get('current_price'),
                'target_price': signal.get('target_price'),
                'confidence': signal.get('confidence', 0.8),
                'timestamp': datetime.now(),
                'description': signal.get('description', ''),
                'factor_name': signal.get('factor_name', ''),
                'factor_value': signal.get('factor_value')
            }

            # 发送飞书通知 - 使用现有的send_card_message方法
            card_content = self._build_signal_card(notification_data)
            self.feishu_notifier.send_card_message(card_content, stock_info['stock_code'])

        except Exception as e:
            self.logger.error(f"❌ 发送信号通知失败: {e}")

    def _build_signal_card(self, notification_data: Dict) -> Dict:
        """构建信号通知卡片"""
        try:
            # 提取信号数据
            stock_code = notification_data.get('stock_code', '')
            stock_name = notification_data.get('stock_name', '')
            signal_type = notification_data.get('signal_type', '')
            current_price = notification_data.get('current_price', 0)
            target_price = notification_data.get('target_price', 0)
            confidence = notification_data.get('confidence', 0)
            factor_name = notification_data.get('factor_name', '')
            factor_value = notification_data.get('factor_value', '')
            description = notification_data.get('description', '')
            timestamp = notification_data.get('timestamp', datetime.now())

            # 信号类型映射
            signal_type_map = {
                'buy': '🟢 买入信号',
                'sell': '🔴 卖出信号',
                'hold': '🟡 持有信号',
                'volume': '📈 成交量异动',
                'price': '💰 价格异动',
                'technical': '📊 技术信号'
            }

            signal_display = signal_type_map.get(signal_type, f'📊 {signal_type}')

            # 置信度显示
            confidence_emoji = "🔥" if confidence >= 0.8 else "⚡" if confidence >= 0.6 else "💡"

            # 价格变动计算
            price_change = ""
            if target_price and current_price:
                change_pct = ((target_price - current_price) / current_price) * 100
                price_change = f" → 目标: ¥{target_price:.2f} ({change_pct:+.1f}%)"

            # 构建卡片内容
            card_content = {
                "config": {
                    "wide_screen_mode": True
                },
                "header": {
                    "title": {
                        "content": f"📊 盘中信号提醒",
                        "tag": "plain_text"
                    },
                    "template": "blue"
                },
                "elements": [
                    {
                        "tag": "div",
                        "text": {
                            "content": f"**{stock_code} {stock_name}**\n{signal_display} | {confidence_emoji} 置信度: {confidence:.1%}",
                            "tag": "lark_md"
                        }
                    },
                    {
                        "tag": "div",
                        "text": {
                            "content": f"💰 当前价格: ¥{current_price:.2f}{price_change}\n📊 因子: {factor_name}\n📈 因子值: {factor_value}",
                            "tag": "lark_md"
                        }
                    }
                ]
            }

            # 添加描述信息
            if description:
                card_content["elements"].append({
                    "tag": "div",
                    "text": {
                        "content": f"📝 **信号描述**\n{description}",
                        "tag": "lark_md"
                    }
                })

            # 添加时间戳
            card_content["elements"].append({
                "tag": "div",
                "text": {
                    "content": f"⏰ 信号时间: {timestamp.strftime('%Y-%m-%d %H:%M:%S')}",
                    "tag": "lark_md"
                }
            })

            return card_content

        except Exception as e:
            self.logger.error(f"构建信号卡片失败: {e}")
            # 返回简化版本
            return {
                "config": {"wide_screen_mode": True},
                "elements": [{
                    "tag": "div",
                    "text": {
                        "content": f"📊 盘中信号: {notification_data.get('stock_code', '')} - {notification_data.get('signal_type', '')}",
                        "tag": "lark_md"
                    }
                }]
            }
    
    def _start_worker_threads(self):
        """启动工作线程"""
        try:
            # 创建工作线程
            for i in range(self.thread_count):
                thread = threading.Thread(
                    target=self._worker_thread,
                    args=(i,),
                    name=f"IntradayWorker-{i}",
                    daemon=True
                )
                thread.start()
                self.worker_threads.append(thread)

            self.logger.info(f"✅ 启动了 {len(self.worker_threads)} 个工作线程")

        except Exception as e:
            self.logger.error(f"❌ 启动工作线程失败: {e}")

    def _worker_thread(self, thread_id: int):
        """工作线程"""
        self.logger.info(f"🔄 工作线程 {thread_id} 开始运行")

        while self.running:
            try:
                # 从队列获取任务
                stock_info = self.task_queue.get(timeout=1)

                # 处理股票
                self._process_stock(stock_info)

                # 标记任务完成
                self.task_queue.task_done()

            except queue.Empty:
                continue
            except Exception as e:
                self.logger.error(f"❌ 工作线程 {thread_id} 处理异常: {e}")

        self.logger.info(f"🛑 工作线程 {thread_id} 结束运行")
    
    def _main_monitoring_loop(self, active_stocks: List[Dict]):
        """主监控循环"""
        try:
            self.logger.info("🔄 开始主监控循环...")

            while self.running:
                try:
                    # 检查是否为新交易日，清理缓存
                    self._clear_cache_if_new_day()

                    # 检查是否为交易时间
                    if not self._is_trading_time():
                        self.logger.info("⏸️ 非交易时间，暂停监控...")
                        time.sleep(60)  # 非交易时间每分钟检查一次
                        continue

                    # 将股票添加到任务队列
                    for stock_info in active_stocks:
                        if self.running:  # 检查是否还在运行
                            self.task_queue.put(stock_info)

                    # 等待所有任务完成
                    self.task_queue.join()

                    # 休息一段时间再进行下一轮监控
                    time.sleep(self.monitoring_interval)

                    # 定期输出统计信息
                    if hasattr(self, 'last_stats_time'):
                        if (datetime.now() - self.last_stats_time).seconds >= 300:  # 每5分钟
                            self._log_stats()
                            self.last_stats_time = datetime.now()
                    else:
                        self.last_stats_time = datetime.now()

                except KeyboardInterrupt:
                    self.logger.info("📝 收到停止信号")
                    break
                except Exception as e:
                    self.logger.error(f"❌ 主循环异常: {e}")
                    time.sleep(5)

        except Exception as e:
            self.logger.error(f"❌ 主监控循环失败: {e}")

    def _log_stats(self):
        """输出统计信息"""
        try:
            # 获取缓存统计
            cache_stats = self._get_cache_statistics()

            self.logger.info(f"📊 统计信息: 已处理股票 {self.stats['stocks_processed']} 只, "
                           f"发送信号 {self.stats['signals_sent']} 个, "
                           f"因子计算 {self.stats['factors_calculated']} 次")

            if cache_stats:
                self.logger.info(f"💾 缓存统计: 缓存条目 {cache_stats['cache_entries']} 个, "
                               f"内存使用 {cache_stats['cache_memory_mb']:.2f} MB, "
                               f"缓存日期 {cache_stats['cache_date']}")

        except Exception as e:
            self.logger.error(f"❌ 输出统计信息失败: {e}")
    
    def stop(self):
        """停止监控进程"""
        self.running = False
        self.logger.info("📊 模块化盘中选股监控进程已停止")
    
    def get_factor_info(self) -> str:
        """获取因子信息"""
        info_lines = ["📊 已注册的因子列表:"]
        
        for factor_name in self.factor_manager.get_factor_list():
            factor_info = self.factor_manager.get_factor_info(factor_name)
            if factor_info:
                info_lines.append(f"\n{factor_name}:")
                info_lines.append(factor_info)
        
        return "\n".join(info_lines)


def main():
    """主函数"""
    monitor = ModularIntradayStockMonitor()
    
    # 显示因子信息
    print(monitor.get_factor_info())
    
    # 启动监控（在实际使用中）
    # monitor.start()


if __name__ == "__main__":
    main()
