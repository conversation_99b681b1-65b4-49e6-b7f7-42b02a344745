#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EMA12通道穿越因子使用示例

展示如何使用EMA12通道穿越因子进行股票技术分析。
包含不同使用场景和配置方式的示例。

作者: QuantFM Team
创建时间: 2025-08-31
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# 添加项目路径
sys.path.append('..')

def generate_sample_data(symbol: str = "000001", length: int = 500) -> pd.DataFrame:
    """生成示例股票数据"""
    np.random.seed(42)
    
    # 生成日期序列
    dates = pd.date_range(start='2023-01-01', periods=length, freq='D')
    
    # 生成价格数据（模拟真实股票走势）
    base_price = 100
    trend = np.linspace(0, 30, length)  # 长期上升趋势
    
    # 添加周期性波动
    cycle1 = 10 * np.sin(np.linspace(0, 4*np.pi, length))  # 长周期
    cycle2 = 5 * np.sin(np.linspace(0, 12*np.pi, length))   # 短周期
    noise = np.random.normal(0, 2, length)  # 随机噪音
    
    close_prices = base_price + trend + cycle1 + cycle2 + noise
    
    # 生成OHLCV数据
    df = pd.DataFrame({
        'trade_time': dates,
        'open': close_prices * (1 + np.random.normal(0, 0.005, length)),
        'high': close_prices * (1 + np.abs(np.random.normal(0, 0.015, length))),
        'low': close_prices * (1 - np.abs(np.random.normal(0, 0.015, length))),
        'close': close_prices,
        'volume': np.random.randint(1000000, 10000000, length),
        'symbol': symbol
    })
    
    # 确保OHLC关系正确
    df['high'] = np.maximum(df['high'], np.maximum(df['open'], df['close']))
    df['low'] = np.minimum(df['low'], np.minimum(df['open'], df['close']))
    
    return df

def example_basic_usage():
    """示例1: 基本使用方法"""
    print("=" * 60)
    print("示例1: EMA12通道穿越因子基本使用")
    print("=" * 60)
    
    from factors.ema.ema12_channel_crossover import EMA12ChannelCrossoverFactor
    
    # 生成示例数据
    df = generate_sample_data("000001", 400)
    print(f"📊 生成示例数据: {len(df)} 条记录")
    
    # 创建因子实例（使用默认配置）
    factor = EMA12ChannelCrossoverFactor()
    
    # 计算因子
    result_df = factor.calculate(df)
    
    # 查看结果
    print(f"✅ 因子计算完成，结果包含 {len(result_df.columns)} 列")
    
    # 显示因子统计信息
    factor_names = factor.get_factor_names()
    print("\n📈 因子统计信息:")
    for name in factor_names:
        if name in result_df.columns:
            non_zero = (result_df[name] != 0).sum()
            mean_val = result_df[name].mean()
            max_val = result_df[name].max()
            print(f"   {name}: 非零值={non_zero}, 均值={mean_val:.4f}, 最大值={max_val:.4f}")
    
    return result_df

def example_boolean_signals():
    """示例2: 布尔信号模式"""
    print("\n" + "=" * 60)
    print("示例2: 布尔信号模式 - 策略信号生成")
    print("=" * 60)
    
    from factors.ema.ema12_channel_crossover import EMA12ChannelCrossoverFactor, EMA12ChannelConfig
    
    # 配置布尔信号模式
    config = EMA12ChannelConfig(
        output_mode="boolean",
        normalize_output=False,
        crossover_threshold=0.001,  # 降低阈值增加信号敏感度
        turnaround_threshold=0.003,
        volume_confirmation=True
    )
    
    factor = EMA12ChannelCrossoverFactor(config=config)
    
    # 生成数据并计算
    df = generate_sample_data("000002", 400)
    result_df = factor.calculate(df)
    
    # 统计信号
    signal_names = factor.get_factor_names()
    print("📊 信号统计:")
    total_signals = 0
    for name in signal_names:
        if name in result_df.columns:
            signal_count = result_df[name].sum()
            total_signals += signal_count
            print(f"   {name}: {signal_count} 个信号")
    
    print(f"   总信号数: {total_signals}")
    print(f"   信号频率: {total_signals/len(result_df):.2%}")
    
    # 显示最近的信号
    recent_signals = result_df.tail(50)
    has_signal = recent_signals[signal_names].any(axis=1)
    signal_dates = recent_signals[has_signal]
    
    if len(signal_dates) > 0:
        print(f"\n📅 最近的信号 (最后50天中的{len(signal_dates)}个):")
        for idx, row in signal_dates.tail(5).iterrows():
            date = row['trade_time'].strftime('%Y-%m-%d')
            price = row['close']
            active_signals = [name for name in signal_names if row[name]]
            print(f"   {date}: 价格={price:.2f}, 信号={active_signals}")
    
    return result_df

def example_continuous_factors():
    """示例3: 连续因子模式"""
    print("\n" + "=" * 60)
    print("示例3: 连续因子模式 - 机器学习特征")
    print("=" * 60)
    
    from factors.ema.ema12_channel_crossover import EMA12ChannelCrossoverFactor, EMA12ChannelConfig
    
    # 配置连续因子模式
    config = EMA12ChannelConfig(
        output_mode="continuous",
        normalize_output=True,
        feature_engineering=True,
        smoothing_window=5
    )
    
    factor = EMA12ChannelCrossoverFactor(config=config)
    
    # 生成数据并计算
    df = generate_sample_data("000003", 400)
    result_df = factor.calculate(df)
    
    # 分析因子分布
    factor_names = factor.get_factor_names()
    print("📊 因子分布统计:")
    for name in factor_names:
        if name in result_df.columns:
            data = result_df[name]
            print(f"   {name}:")
            print(f"     均值: {data.mean():.4f}")
            print(f"     标准差: {data.std():.4f}")
            print(f"     最小值: {data.min():.4f}")
            print(f"     最大值: {data.max():.4f}")
            print(f"     25%分位: {data.quantile(0.25):.4f}")
            print(f"     75%分位: {data.quantile(0.75):.4f}")
    
    # 特征重要性
    importance = factor.get_feature_importance()
    print("\n🎯 特征重要性:")
    for name, weight in importance.items():
        print(f"   {name}: {weight:.2f}")
    
    return result_df

def example_custom_config():
    """示例4: 自定义配置"""
    print("\n" + "=" * 60)
    print("示例4: 自定义配置 - 参数调优")
    print("=" * 60)
    
    from factors.ema.ema12_channel_crossover import EMA12ChannelCrossoverFactor, EMA12ChannelConfig
    
    # 自定义配置：更敏感的参数设置
    custom_config = EMA12ChannelConfig(
        # 调整EMA周期
        fast_period=10,           # 更快的EMA
        slow_period1=120,         # 稍快的慢速EMA
        slow_period2=150,
        
        # 调整检测阈值
        crossover_threshold=0.0015,    # 更敏感的穿越检测
        turnaround_threshold=0.003,    # 更敏感的转头检测
        turnaround_lookback=3,         # 更短的回看期
        
        # 调整信号强度权重
        price_momentum_weight=0.5,     # 增加价格动量权重
        volume_momentum_weight=0.2,    # 减少成交量权重
        ema_momentum_weight=0.3,
        
        # 输出设置
        output_mode="continuous",
        normalize_output=True,
        smoothing_window=3
    )
    
    factor = EMA12ChannelCrossoverFactor(config=custom_config)
    
    # 生成数据并计算
    df = generate_sample_data("000004", 400)
    result_df = factor.calculate(df)
    
    # 对比默认配置
    default_factor = EMA12ChannelCrossoverFactor()
    default_result = default_factor.calculate(df)
    
    print("📊 自定义配置 vs 默认配置对比:")
    factor_names = factor.get_factor_names()
    
    for name in factor_names:
        if name in result_df.columns and name in default_result.columns:
            custom_mean = result_df[name].mean()
            default_mean = default_result[name].mean()
            custom_std = result_df[name].std()
            default_std = default_result[name].std()
            
            print(f"   {name}:")
            print(f"     自定义: 均值={custom_mean:.4f}, 标准差={custom_std:.4f}")
            print(f"     默认:   均值={default_mean:.4f}, 标准差={default_std:.4f}")
            print(f"     差异:   均值差={custom_mean-default_mean:.4f}")
    
    return result_df, default_result

def example_signal_validation():
    """示例5: 信号质量验证"""
    print("\n" + "=" * 60)
    print("示例5: 信号质量验证")
    print("=" * 60)
    
    from factors.ema.ema12_channel_crossover import EMA12ChannelCrossoverFactor
    
    # 创建因子并计算
    factor = EMA12ChannelCrossoverFactor()
    df = generate_sample_data("000005", 400)
    result_df = factor.calculate(df)
    
    # 验证信号质量
    validation_results = factor.validate_signals(result_df)
    
    print("📊 信号质量验证报告:")
    print(f"   数据质量: {validation_results.get('data_quality', 'unknown')}")
    print(f"   平均信号强度: {validation_results.get('avg_signal_strength', 0):.4f}")
    
    # 详细的信号分布
    signal_dist = validation_results.get('signal_distribution', {})
    if signal_dist:
        print("\n📈 信号分布详情:")
        for signal_name, dist_info in signal_dist.items():
            if isinstance(dist_info, dict):
                print(f"   {signal_name}:")
                print(f"     均值: {dist_info.get('mean', 0):.4f}")
                print(f"     标准差: {dist_info.get('std', 0):.4f}")
                print(f"     最大值: {dist_info.get('max', 0):.4f}")
                print(f"     最小值: {dist_info.get('min', 0):.4f}")
    
    return validation_results

def main():
    """主函数：运行所有示例"""
    print("🚀 EMA12通道穿越因子使用示例")
    print(f"运行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 运行所有示例
        example_basic_usage()
        example_boolean_signals()
        example_continuous_factors()
        example_custom_config()
        example_signal_validation()
        
        print("\n" + "=" * 60)
        print("🎉 所有示例运行完成！")
        print("=" * 60)
        
        print("\n📋 使用要点总结:")
        print("1. 基本使用：直接创建EMA12ChannelCrossoverFactor实例")
        print("2. 布尔模式：适用于策略信号生成，output_mode='boolean'")
        print("3. 连续模式：适用于机器学习特征，output_mode='continuous'")
        print("4. 自定义配置：通过EMA12ChannelConfig调整参数")
        print("5. 信号验证：使用validate_signals()检查信号质量")
        
        print("\n🔧 配置建议:")
        print("- 策略交易：使用布尔模式，适当提高阈值减少噪音")
        print("- 机器学习：使用连续模式，启用标准化和特征工程")
        print("- 回测分析：禁用成交量确认，减少平滑窗口")
        print("- 实时交易：启用缓存，适当增加平滑窗口")
        
    except Exception as e:
        print(f"❌ 示例运行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
