#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
复合因子插件

定义复合因子的插件接口
"""

from typing import Dict, Any
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from plugins.base_plugin import BaseFactorPlugin, PluginInfo


class TechnicalCompositeFactorPlugin(BaseFactorPlugin):
    """技术分析复合因子插件"""
    
    @classmethod
    def get_plugin_info(cls) -> PluginInfo:
        return PluginInfo(
            name="technical_composite_factor",
            version="2.0.0",
            description="技术分析复合因子，综合趋势、支撑阻力和成交量信号",
            author="QuantFM Team",
            category="composite",
            factor_class="EmaTurnaroundCompositeFactor",
            factor_module="factors.ema_turnaround_composite_factor",
            dependencies=["close", "high", "low", "volume"],
            scenarios=["after_market", "research"],  # 复合因子适合深度分析
            computational_cost="high",
            priority="medium",
            tags=["composite", "technical", "multi_factor"],
            
            # 高级特性
            qlib_compatible=True,
            trainable=True,
            optimizable=True,
            vectorized=False,  # 复合因子通常不是向量化的
            
            # 性能指标
            min_data_length=300,
            max_lookback=252,
            memory_usage="high",
            
            # 默认配置
            default_config={
                'ema_periods': [12, 62, 144, 169],
                'fibonacci_lookback': 252,
                'bollinger_period': 20,
                'volume_lookback': 20,
                'trend_weight': 0.4,
                'support_resistance_weight': 0.4,
                'volume_weight': 0.2
            }
        )


class MomentumCompositeFactorPlugin(BaseFactorPlugin):
    """动量复合因子插件"""
    
    @classmethod
    def get_plugin_info(cls) -> PluginInfo:
        return PluginInfo(
            name="momentum_composite_factor",
            version="2.0.0",
            description="动量复合因子，专注于价格动量和趋势强度",
            author="QuantFM Team",
            category="composite",
            factor_class="EmaTurnaroundCompositeFactor",  # 复用现有实现
            factor_module="factors.ema_turnaround_composite_factor",
            dependencies=["close", "volume"],
            scenarios=["swing", "position"],
            computational_cost="medium",
            priority="medium",
            tags=["composite", "momentum", "trend_strength"],
            
            # 高级特性
            qlib_compatible=True,
            trainable=True,
            optimizable=True,
            vectorized=False,
            
            # 性能指标
            min_data_length=100,
            max_lookback=200,
            memory_usage="medium",
            
            # 默认配置
            default_config={
                'ema_periods': [12, 26],
                'macd_fast': 12,
                'macd_slow': 26,
                'macd_signal': 9,
                'volume_lookback': 20,
                'momentum_weight': 0.7,
                'volume_weight': 0.3
            }
        )
