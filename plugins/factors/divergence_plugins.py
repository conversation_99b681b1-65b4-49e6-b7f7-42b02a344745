#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
背离检测因子插件

定义各种背离检测因子的插件接口
"""

from typing import Dict, Any
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from plugins.base_plugin import BaseFactorPlugin, PluginInfo


class MacdDivergenceFactorPlugin(BaseFactorPlugin):
    """MACD背离因子插件"""
    
    @classmethod
    def get_plugin_info(cls) -> PluginInfo:
        return PluginInfo(
            name="macd_divergence_factor",
            version="2.0.0",
            description="MACD背离因子，检测MACD与价格的背离信号",
            author="QuantFM Team",
            category="divergence",
            factor_class="MACDDivergenceFactor",
            factor_module="factors.divergence.macd_divergence",
            dependencies=["close"],
            scenarios=["intraday", "swing", "after_market"],
            computational_cost="medium",
            priority="high",
            tags=["divergence", "macd", "momentum"],
            
            # 高级特性
            qlib_compatible=True,
            trainable=True,
            optimizable=True,
            vectorized=True,
            
            # 性能指标
            min_data_length=100,
            max_lookback=200,
            memory_usage="medium",
            
            # 默认配置
            default_config={
                'fastperiod': 12,
                'slowperiod': 26,
                'signalperiod': 9,
                'window': 5,
                'min_bars_between': 20,
                'output_mode': 'boolean'
            }
        )


class RsiDivergenceFactorPlugin(BaseFactorPlugin):
    """RSI背离因子插件"""
    
    @classmethod
    def get_plugin_info(cls) -> PluginInfo:
        return PluginInfo(
            name="rsi_divergence_factor",
            version="2.0.0",
            description="RSI背离因子，检测RSI与价格的背离信号",
            author="QuantFM Team",
            category="divergence",
            factor_class="RSIDivergenceFactor",
            factor_module="factors.divergence.rsi_divergence",
            dependencies=["close"],
            scenarios=["intraday", "swing", "after_market"],
            computational_cost="medium",
            priority="medium",
            tags=["divergence", "rsi", "momentum"],
            
            # 高级特性
            qlib_compatible=True,
            trainable=True,
            optimizable=True,
            vectorized=True,
            
            # 性能指标
            min_data_length=50,
            max_lookback=100,
            memory_usage="low",
            
            # 默认配置
            default_config={
                'period': 14,
                'window': 5,
                'min_bars_between': 20,
                'output_mode': 'boolean'
            }
        )


class KdjDivergenceFactorPlugin(BaseFactorPlugin):
    """KDJ背离因子插件"""
    
    @classmethod
    def get_plugin_info(cls) -> PluginInfo:
        return PluginInfo(
            name="kdj_divergence_factor",
            version="2.0.0",
            description="KDJ背离因子，检测KDJ与价格的背离信号",
            author="QuantFM Team",
            category="divergence",
            factor_class="KDJDivergenceFactor",
            factor_module="factors.divergence.kdj_divergence",
            dependencies=["close", "high", "low"],
            scenarios=["intraday", "swing", "after_market"],
            computational_cost="medium",
            priority="medium",
            tags=["divergence", "kdj", "stochastic"],
            
            # 高级特性
            qlib_compatible=True,
            trainable=True,
            optimizable=True,
            vectorized=True,
            
            # 性能指标
            min_data_length=50,
            max_lookback=100,
            memory_usage="low",
            
            # 默认配置
            default_config={
                'fastk_period': 9,
                'slowk_period': 3,
                'slowd_period': 3,
                'window': 5,
                'min_bars_between': 20,
                'output_mode': 'boolean'
            }
        )
