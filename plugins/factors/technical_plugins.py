#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
技术指标因子插件

定义技术指标类因子的插件接口
"""

from typing import Dict, Any
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from plugins.base_plugin import BaseFactorPlugin, PluginInfo


class EmaFactorPlugin(BaseFactorPlugin):
    """EMA因子插件"""
    
    @classmethod
    def get_plugin_info(cls) -> PluginInfo:
        return PluginInfo(
            name="ema_factor",
            version="2.0.0",
            description="EMA均线因子，检测价格是否接近关键EMA均线",
            author="QuantFM Team",
            category="technical",
            factor_class="EmaFactor",
            factor_module="factors.ema_factor",
            dependencies=["close"],
            scenarios=["intraday", "swing", "position"],
            computational_cost="low",
            priority="high",
            tags=["trend", "ema", "moving_average"],
            
            # 高级特性
            qlib_compatible=True,
            trainable=False,
            optimizable=True,
            vectorized=True,
            
            # 性能指标
            min_data_length=50,
            max_lookback=676,
            memory_usage="low",
            
            # 默认配置
            default_config={
                'periods': [12, 62, 144, 169],
                'price_threshold': 0.005,
                'weight_decay': 0.1,
                'min_periods': 3
            }
        )


class BollingerBandsFactorPlugin(BaseFactorPlugin):
    """布林带因子插件"""
    
    @classmethod
    def get_plugin_info(cls) -> PluginInfo:
        return PluginInfo(
            name="bollinger_bands_factor",
            version="3.0.0",
            description="高阶布林带因子，支持波动率压缩扩张检测、突破真假判断等",
            author="QuantFM Team",
            category="technical",
            factor_class="BollingerFactor",
            factor_module="factors.bollinger_factor",
            dependencies=["close", "volume", "high", "low"],
            scenarios=["intraday", "swing", "after_market"],
            computational_cost="medium",
            priority="high",
            tags=["volatility", "bollinger", "support_resistance", "advanced"],
            
            # 高级特性
            qlib_compatible=True,
            trainable=True,
            optimizable=True,
            vectorized=True,
            
            # 性能指标
            min_data_length=300,
            max_lookback=252,
            memory_usage="medium",
            
            # 默认配置
            default_config={
                'period': 20,
                'std_dev': 2.0,
                'bbw_lookback': 252,
                'squeeze_threshold': 0.2,
                'expansion_threshold': 0.8,
                'breakout_volume_ratio': 1.2,
                'walking_days': 3,
                'walking_tolerance': 0.02,
                'trend_confirmation': True,
                'output_mode': 'boolean'
            }
        )


class FibonacciFactorPlugin(BaseFactorPlugin):
    """斐波那契因子插件"""
    
    @classmethod
    def get_plugin_info(cls) -> PluginInfo:
        return PluginInfo(
            name="fibonacci_factor",
            version="2.0.0",
            description="斐波那契回撤位因子，检测价格是否接近关键斐波那契位",
            author="QuantFM Team",
            category="technical",
            factor_class="FibonacciFactor",
            factor_module="factors.fibonacci_factor",
            dependencies=["close", "high", "low"],
            scenarios=["intraday", "swing", "position"],
            computational_cost="medium",
            priority="high",
            tags=["support_resistance", "fibonacci", "retracement"],
            
            # 高级特性
            qlib_compatible=True,
            trainable=False,
            optimizable=True,
            vectorized=True,
            
            # 性能指标
            min_data_length=100,
            max_lookback=252,
            memory_usage="medium",
            
            # 默认配置
            default_config={
                'lookback_period': 252,
                'price_threshold': 0.005,
                'fib_levels': [0.236, 0.382, 0.5, 0.618, 0.786],
                'min_swing_size': 0.05
            }
        )
