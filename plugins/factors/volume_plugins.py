#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
成交量类因子插件

定义成交量相关因子的插件接口
"""

from typing import Dict, Any
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from plugins.base_plugin import BaseFactorPlugin, PluginInfo


class VolumeSpikeFactorPlugin(BaseFactorPlugin):
    """成交量激增因子插件"""
    
    @classmethod
    def get_plugin_info(cls) -> PluginInfo:
        return PluginInfo(
            name="volume_spike_factor",
            version="2.0.0",
            description="成交量激增因子，检测异常的成交量放大",
            author="QuantFM Team",
            category="volume",
            factor_class="VolumeSpikeFactor",
            factor_module="factors.volume_factor",
            dependencies=["volume"],
            scenarios=["intraday", "swing"],
            computational_cost="low",
            priority="high",
            tags=["volume", "anomaly", "spike"],
            
            # 高级特性
            qlib_compatible=True,
            trainable=True,
            optimizable=True,
            vectorized=True,
            
            # 性能指标
            min_data_length=50,
            max_lookback=100,
            memory_usage="low",
            
            # 默认配置
            default_config={
                'lookback_period': 20,
                'spike_threshold': 2.0,
                'method': 'mean',  # mean, median, percentile
                'percentile_threshold': 0.9
            }
        )


class PriceVolumeFactorPlugin(BaseFactorPlugin):
    """价量关系因子插件"""
    
    @classmethod
    def get_plugin_info(cls) -> PluginInfo:
        return PluginInfo(
            name="price_volume_factor",
            version="2.0.0",
            description="价量关系因子，分析价格变动与成交量的关系",
            author="QuantFM Team",
            category="volume",
            factor_class="PriceVolumeFactor",
            factor_module="factors.volume_factor",
            dependencies=["close", "volume"],
            scenarios=["intraday", "swing", "after_market"],
            computational_cost="medium",
            priority="medium",
            tags=["volume", "price_action", "correlation"],
            
            # 高级特性
            qlib_compatible=True,
            trainable=True,
            optimizable=True,
            vectorized=True,
            
            # 性能指标
            min_data_length=50,
            max_lookback=100,
            memory_usage="medium",
            
            # 默认配置
            default_config={
                'lookback_period': 20,
                'correlation_threshold': 0.3,
                'divergence_threshold': 0.5
            }
        )
