#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
配置管理器

统一管理插件和因子配置，优先使用TOML格式
"""

import os
import tomllib
import yaml
from pathlib import Path
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, field
import threading
from copy import deepcopy

from utils.logger import get_logger


@dataclass
class ScenarioConfig:
    """场景配置"""
    name: str
    description: str
    max_factors: int = -1               # -1表示无限制
    max_computational_cost: str = "high"
    enable_optimization: bool = True
    timeout: int = 30
    priority_filter: List[str] = field(default_factory=lambda: ["high", "medium", "low"])
    factors: Dict[str, Dict[str, Any]] = field(default_factory=dict)


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_dir: str = "config"):
        self.logger = get_logger('ConfigManager')
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(parents=True, exist_ok=True)
        
        # 配置缓存
        self._config_cache: Dict[str, Dict[str, Any]] = {}
        self._scenarios: Dict[str, ScenarioConfig] = {}
        self._lock = threading.RLock()
        
        # 配置文件路径
        self.main_config_file = self.config_dir / "plugins.toml"
        self.factors_config_file = self.config_dir / "factors.toml"
        self.qlib_config_file = self.config_dir / "qlib.yaml"  # Qlib专用YAML
        
        # 初始化
        self._load_configurations()
        
        self.logger.info(f"配置管理器初始化完成，配置目录: {self.config_dir}")
    
    def _load_configurations(self):
        """加载所有配置"""
        try:
            # 加载主配置
            self._load_main_config()
            
            # 加载因子配置
            self._load_factors_config()
            
            # 加载Qlib配置（如果存在）
            self._load_qlib_config()
            
            # 构建场景配置
            self._build_scenarios()
            
        except Exception as e:
            self.logger.error(f"加载配置失败: {e}")
            self._create_default_configs()
    
    def _load_main_config(self):
        """加载主配置文件"""
        try:
            if self.main_config_file.exists():
                with open(self.main_config_file, 'rb') as f:
                    main_config = tomllib.load(f)
            else:
                main_config = self._create_default_main_config()
                self._save_toml_config(main_config, self.main_config_file)
            
            with self._lock:
                self._config_cache['main'] = main_config
            
            self.logger.info("主配置加载完成")
            
        except Exception as e:
            self.logger.error(f"加载主配置失败: {e}")
            with self._lock:
                self._config_cache['main'] = self._create_default_main_config()
    
    def _load_factors_config(self):
        """加载因子配置文件"""
        try:
            if self.factors_config_file.exists():
                with open(self.factors_config_file, 'rb') as f:
                    factors_config = tomllib.load(f)
            else:
                factors_config = self._create_default_factors_config()
                self._save_toml_config(factors_config, self.factors_config_file)
            
            with self._lock:
                self._config_cache['factors'] = factors_config
            
            self.logger.info("因子配置加载完成")
            
        except Exception as e:
            self.logger.error(f"加载因子配置失败: {e}")
            with self._lock:
                self._config_cache['factors'] = self._create_default_factors_config()
    
    def _load_qlib_config(self):
        """加载Qlib配置文件"""
        try:
            if self.qlib_config_file.exists():
                with open(self.qlib_config_file, 'r', encoding='utf-8') as f:
                    qlib_config = yaml.safe_load(f) or {}
            else:
                qlib_config = self._create_default_qlib_config()
                self._save_yaml_config(qlib_config, self.qlib_config_file)
            
            with self._lock:
                self._config_cache['qlib'] = qlib_config
            
            self.logger.info("Qlib配置加载完成")
            
        except Exception as e:
            self.logger.error(f"加载Qlib配置失败: {e}")
            with self._lock:
                self._config_cache['qlib'] = self._create_default_qlib_config()
    
    def _build_scenarios(self):
        """构建场景配置"""
        try:
            main_config = self._config_cache.get('main', {})
            scenarios_data = main_config.get('scenarios', {})
            
            with self._lock:
                self._scenarios.clear()
                
                for scenario_name, scenario_data in scenarios_data.items():
                    scenario = ScenarioConfig(
                        name=scenario_name,
                        description=scenario_data.get('description', ''),
                        max_factors=scenario_data.get('max_factors', -1),
                        max_computational_cost=scenario_data.get('max_computational_cost', 'high'),
                        enable_optimization=scenario_data.get('enable_optimization', True),
                        timeout=scenario_data.get('timeout', 30),
                        priority_filter=scenario_data.get('priority_filter', ['high', 'medium', 'low']),
                        factors=scenario_data.get('factors', {})
                    )
                    self._scenarios[scenario_name] = scenario
            
            self.logger.info(f"构建了 {len(self._scenarios)} 个场景配置")
            
        except Exception as e:
            self.logger.error(f"构建场景配置失败: {e}")
    
    def _create_default_main_config(self) -> Dict[str, Any]:
        """创建默认主配置"""
        return {
            'system': {
                'version': '2.0.0',
                'enable_caching': True,
                'cache_size': 1000,
                'enable_optimization': True,
                'enable_qlib': False,
                'default_scenario': 'intraday',
                'performance_monitoring': True,
                'log_level': 'INFO'
            },
            'performance': {
                'max_concurrent_factors': 10,
                'factor_timeout': 30,
                'memory_limit': '1GB',
                'enable_vectorization': True,
                'numpy_threads': 4
            },
            'scenarios': {
                'intraday': {
                    'description': '盘中实时监控',
                    'max_factors': 8,
                    'max_computational_cost': 'medium',
                    'enable_optimization': False,
                    'timeout': 5,
                    'priority_filter': ['high', 'medium']
                },
                'after_market': {
                    'description': '盘后分析',
                    'max_factors': 15,
                    'max_computational_cost': 'high',
                    'enable_optimization': True,
                    'timeout': 30,
                    'priority_filter': ['high', 'medium', 'low']
                },
                'research': {
                    'description': '研究分析',
                    'max_factors': -1,
                    'max_computational_cost': 'high',
                    'enable_optimization': True,
                    'timeout': 60,
                    'priority_filter': ['high', 'medium', 'low']
                }
            }
        }
    
    def _create_default_factors_config(self) -> Dict[str, Any]:
        """创建默认因子配置"""
        return {
            'defaults': {
                'enabled': True,
                'priority': 'medium',
                'computational_cost': 'medium',
                'cache_results': True,
                'enable_validation': True,
                'timeout': 10
            },
            'categories': {
                'technical': {
                    'max_factors_per_scenario': {
                        'intraday': 5,
                        'after_market': 10,
                        'research': -1
                    }
                },
                'volume': {
                    'max_factors_per_scenario': {
                        'intraday': 2,
                        'after_market': 5,
                        'research': -1
                    }
                },
                'composite': {
                    'max_factors_per_scenario': {
                        'intraday': 1,
                        'after_market': 3,
                        'research': -1
                    }
                }
            },
            'factors': {
                'ema_factor': {
                    'enabled': True,
                    'priority': 'high',
                    'periods': [12, 62, 144, 169],
                    'price_threshold': 0.005
                },
                'bollinger_bands_factor': {
                    'enabled': True,
                    'priority': 'high',
                    'period': 20,
                    'std_dev': 2.0
                },
                'volume_spike_factor': {
                    'enabled': True,
                    'priority': 'high',
                    'lookback_period': 20,
                    'spike_threshold': 2.0
                }
            }
        }
    
    def _create_default_qlib_config(self) -> Dict[str, Any]:
        """创建默认Qlib配置"""
        return {
            'qlib': {
                'provider_uri': 'file:///data/qlib',
                'region': 'cn',
                'auto_mount': True,
                'flask_server': False,
                'logging_level': 'INFO'
            },
            'data': {
                'calendar_provider': 'LocalCalendarProvider',
                'instrument_provider': 'LocalInstrumentProvider',
                'feature_provider': 'LocalFeatureProvider'
            },
            'factors': {
                'qlib_compatible_factors': [
                    'ema_factor',
                    'bollinger_bands_factor'
                ],
                'expression_factors': {
                    'close_price': 'Close',
                    'volume': 'Volume',
                    'returns': 'Ref(Close, 0) / Ref(Close, 1) - 1'
                }
            }
        }
    
    def _save_toml_config(self, config: Dict[str, Any], file_path: Path):
        """保存TOML配置"""
        try:
            import tomli_w
            with open(file_path, 'wb') as f:
                tomli_w.dump(config, f)
        except ImportError:
            # 如果没有tomli_w，使用简单的字符串格式化
            self.logger.warning("tomli_w不可用，使用简化TOML写入")
            self._save_simple_toml(config, file_path)
    
    def _save_yaml_config(self, config: Dict[str, Any], file_path: Path):
        """保存YAML配置"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
        except Exception as e:
            self.logger.error(f"保存YAML配置失败: {e}")
    
    def _save_simple_toml(self, config: Dict[str, Any], file_path: Path):
        """简化TOML保存（备用方案）"""
        # 这里可以实现一个简单的TOML写入器
        # 或者提示用户安装tomli_w
        self.logger.warning(f"无法保存TOML配置到 {file_path}，请安装tomli_w")
    
    def _create_default_configs(self):
        """创建默认配置（错误恢复）"""
        with self._lock:
            self._config_cache['main'] = self._create_default_main_config()
            self._config_cache['factors'] = self._create_default_factors_config()
            self._config_cache['qlib'] = self._create_default_qlib_config()
        
        self._build_scenarios()
    
    def get_scenario_config(self, scenario: str) -> Optional[ScenarioConfig]:
        """获取场景配置"""
        with self._lock:
            return self._scenarios.get(scenario)
    
    def get_factor_config(self, factor_name: str, scenario: str = None) -> Dict[str, Any]:
        """获取因子配置"""
        with self._lock:
            factors_config = self._config_cache.get('factors', {})
            
            # 获取默认配置
            defaults = factors_config.get('defaults', {})
            
            # 获取因子特定配置
            factor_config = factors_config.get('factors', {}).get(factor_name, {})
            
            # 合并配置
            final_config = deepcopy(defaults)
            final_config.update(factor_config)
            
            # 应用场景特定配置
            if scenario and scenario in self._scenarios:
                scenario_obj = self._scenarios[scenario]
                if factor_name in scenario_obj.factors:
                    final_config.update(scenario_obj.factors[factor_name])
            
            return final_config
    
    def get_qlib_config(self) -> Dict[str, Any]:
        """获取Qlib配置"""
        with self._lock:
            return deepcopy(self._config_cache.get('qlib', {}))
    
    def list_scenarios(self) -> List[str]:
        """列出所有场景"""
        with self._lock:
            return list(self._scenarios.keys())


# 全局配置管理器实例
_config_manager = None

def get_config_manager() -> ConfigManager:
    """获取全局配置管理器实例"""
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager()
    return _config_manager
