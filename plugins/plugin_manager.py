#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
插件管理器

负责插件的发现、加载、管理和因子实例创建
"""

import os
import sys
import importlib
import inspect
from pathlib import Path
from typing import Dict, List, Any, Optional

from utils.logger import get_logger
from .base_plugin import BaseFactorPlugin, PluginInfo, get_factor_registry


class PluginManager:
    """插件管理器"""
    
    def __init__(self, plugin_dirs: List[str] = None):
        self.logger = get_logger('PluginManager')
        
        # 默认插件目录
        if plugin_dirs is None:
            plugin_dirs = [
                'plugins/factors',  # 新的插件目录
                'plugins/custom'    # 自定义插件目录
            ]
        
        self.plugin_dirs = [Path(d) for d in plugin_dirs]
        self.plugins: Dict[str, BaseFactorPlugin] = {}
        self.plugin_info: Dict[str, PluginInfo] = {}
        self.factor_registry = get_factor_registry()
        
        # 确保插件目录存在
        for plugin_dir in self.plugin_dirs:
            plugin_dir.mkdir(parents=True, exist_ok=True)
        
        # 自动发现和加载插件
        self.discover_plugins()
        
        self.logger.info(f"插件管理器初始化完成，发现 {len(self.plugins)} 个插件")
    
    def discover_plugins(self):
        """发现并加载所有插件"""
        self.logger.info("开始发现插件...")
        
        for plugin_dir in self.plugin_dirs:
            if not plugin_dir.exists():
                continue
                
            self.logger.debug(f"扫描插件目录: {plugin_dir}")
            self._scan_directory(plugin_dir)
        
        self.logger.info(f"插件发现完成，共加载 {len(self.plugins)} 个插件")
    
    def _scan_directory(self, directory: Path):
        """扫描目录中的插件文件"""
        try:
            # 确保项目根目录在Python路径中
            project_root = Path.cwd()
            if str(project_root) not in sys.path:
                sys.path.insert(0, str(project_root))

            # 扫描Python文件
            for py_file in directory.glob("**/*.py"):
                if py_file.name.startswith('_'):
                    continue

                try:
                    self._load_plugin_file(py_file, directory)
                except Exception as e:
                    self.logger.error(f"加载插件文件失败 {py_file}: {e}")

        except Exception as e:
            self.logger.error(f"扫描目录失败 {directory}: {e}")
    
    def _load_plugin_file(self, py_file: Path, base_dir: Path):
        """加载单个插件文件"""
        try:
            # 构建模块名 - 修复路径问题
            # 从项目根目录开始构建模块路径
            project_root = Path.cwd()

            try:
                # 尝试相对于当前工作目录
                relative_path = py_file.relative_to(project_root)
                module_name = str(relative_path.with_suffix('')).replace(os.sep, '.')
            except ValueError:
                # 如果失败，使用绝对路径方式
                # 假设插件文件在 plugins/factors/ 下
                if 'plugins' in str(py_file) and 'factors' in str(py_file):
                    # 直接构建模块名
                    module_name = f"plugins.factors.{py_file.stem}"
                else:
                    # 使用相对于base_dir的路径
                    relative_path = py_file.relative_to(base_dir.parent)
                    module_name = str(relative_path.with_suffix('')).replace(os.sep, '.')

            self.logger.debug(f"尝试导入模块: {module_name}")

            # 导入模块
            module = importlib.import_module(module_name)
            
            # 查找插件类
            for name, obj in inspect.getmembers(module, inspect.isclass):
                if (issubclass(obj, BaseFactorPlugin) and 
                    obj != BaseFactorPlugin and 
                    hasattr(obj, 'get_plugin_info')):
                    
                    try:
                        plugin_info = obj.get_plugin_info()
                        self._register_plugin(obj, plugin_info)
                    except Exception as e:
                        self.logger.error(f"注册插件失败 {name}: {e}")
                        
        except Exception as e:
            self.logger.error(f"导入插件模块失败 {py_file}: {e}")
    
    def _register_plugin(self, plugin_class: BaseFactorPlugin, plugin_info: PluginInfo):
        """注册插件"""
        plugin_name = plugin_info.name
        
        if plugin_name in self.plugins:
            self.logger.warning(f"插件 {plugin_name} 已存在，跳过注册")
            return
        
        # 注册插件
        self.plugins[plugin_name] = plugin_class
        self.plugin_info[plugin_name] = plugin_info
        
        # 注册因子到因子注册表
        try:
            factor_module = importlib.import_module(plugin_info.factor_module)
            factor_class = getattr(factor_module, plugin_info.factor_class)
            self.factor_registry.register_factor(
                plugin_name, 
                factor_class, 
                plugin_info.factor_module
            )
        except Exception as e:
            self.logger.warning(f"注册因子到注册表失败 {plugin_name}: {e}")
        
        self.logger.info(f"注册插件: {plugin_name} v{plugin_info.version}")
    
    def get_plugin(self, plugin_name: str) -> Optional[BaseFactorPlugin]:
        """获取插件"""
        return self.plugins.get(plugin_name)
    
    def get_plugin_info(self, plugin_name: str) -> Optional[PluginInfo]:
        """获取插件信息"""
        return self.plugin_info.get(plugin_name)
    
    def list_plugins(self) -> Dict[str, PluginInfo]:
        """列出所有插件"""
        return self.plugin_info.copy()
    
    def get_plugins_by_scenario(self, scenario: str) -> List[str]:
        """根据场景获取适用的插件"""
        suitable_plugins = []
        
        for plugin_name, plugin_info in self.plugin_info.items():
            if scenario in plugin_info.scenarios and plugin_info.enabled:
                suitable_plugins.append(plugin_name)
        
        return suitable_plugins
    
    def get_plugins_by_category(self, category: str) -> List[str]:
        """根据分类获取插件"""
        plugins = []
        
        for plugin_name, plugin_info in self.plugin_info.items():
            if plugin_info.category == category and plugin_info.enabled:
                plugins.append(plugin_name)
        
        return plugins
    
    def create_factor_instance(self, plugin_name: str, config: Dict[str, Any] = None):
        """创建因子实例"""
        if plugin_name not in self.plugins:
            self.logger.error(f"插件不存在: {plugin_name}")
            return None
        
        plugin_class = self.plugins[plugin_name]
        
        try:
            return plugin_class.create_factor(config)
        except Exception as e:
            self.logger.error(f"创建因子实例失败 {plugin_name}: {e}")
            return None
    
    def load_factors_for_scenario(self, scenario: str, 
                                 factor_configs: Dict[str, Dict[str, Any]] = None) -> List:
        """为特定场景加载因子"""
        factors = []
        suitable_plugins = self.get_plugins_by_scenario(scenario)
        
        self.logger.info(f"为场景 '{scenario}' 找到 {len(suitable_plugins)} 个适用插件")
        
        for plugin_name in suitable_plugins:
            try:
                # 获取配置
                config = {}
                if factor_configs and plugin_name in factor_configs:
                    config = factor_configs[plugin_name]
                
                # 创建因子实例
                factor = self.create_factor_instance(plugin_name, config)
                if factor:
                    factors.append(factor)
                    self.logger.debug(f"为场景 '{scenario}' 创建因子: {plugin_name}")
                
            except Exception as e:
                self.logger.error(f"为场景 '{scenario}' 创建因子失败 {plugin_name}: {e}")
        
        self.logger.info(f"为场景 '{scenario}' 成功加载 {len(factors)} 个因子")
        return factors
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取插件统计信息"""
        total_plugins = len(self.plugins)
        enabled_plugins = sum(1 for info in self.plugin_info.values() if info.enabled)
        
        categories = {}
        scenarios = set()
        
        for plugin_info in self.plugin_info.values():
            # 统计分类
            category = plugin_info.category
            categories[category] = categories.get(category, 0) + 1
            
            # 收集场景
            scenarios.update(plugin_info.scenarios)
        
        return {
            'total_plugins': total_plugins,
            'enabled_plugins': enabled_plugins,
            'loaded_factors': len(self.factor_registry.list_factors()),
            'categories': categories,
            'available_scenarios': sorted(list(scenarios))
        }


# 全局插件管理器实例
_plugin_manager = None

def get_plugin_manager() -> PluginManager:
    """获取全局插件管理器实例"""
    global _plugin_manager
    if _plugin_manager is None:
        _plugin_manager = PluginManager()
    return _plugin_manager
