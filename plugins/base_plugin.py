#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
插件基类

定义因子插件的标准接口和基础功能
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from typing import Dict, List, Any, Optional
import importlib

from utils.logger import get_logger


@dataclass
class PluginInfo:
    """插件信息"""
    name: str                           # 插件名称
    version: str                        # 版本号
    description: str                    # 描述
    author: str                         # 作者
    category: str                       # 分类
    factor_class: str                   # 因子类名
    factor_module: str                  # 因子模块路径
    dependencies: List[str]             # 数据依赖
    scenarios: List[str]                # 适用场景
    computational_cost: str             # 计算成本
    priority: str                       # 优先级
    tags: List[str]                     # 标签
    
    # 高级特性
    qlib_compatible: bool = False       # Qlib兼容性
    trainable: bool = False             # 是否可训练
    optimizable: bool = False           # 是否支持参数优化
    vectorized: bool = True             # 是否向量化实现
    
    # 性能指标
    min_data_length: int = 50           # 最小数据长度
    max_lookback: int = 252             # 最大回看期
    memory_usage: str = "low"           # 内存使用
    
    # 配置相关
    config_file: str = ""               # 配置文件路径
    default_config: Dict[str, Any] = field(default_factory=dict)
    
    enabled: bool = True                # 是否启用


class BaseFactorPlugin(ABC):
    """因子插件基类"""
    
    def __init__(self):
        self.logger = get_logger(f'Plugin_{self.get_plugin_info().name}')
    
    @classmethod
    @abstractmethod
    def get_plugin_info(cls) -> PluginInfo:
        """获取插件信息"""
        pass
    
    @classmethod
    def create_factor(cls, config: Dict[str, Any] = None):
        """创建因子实例"""
        plugin_info = cls.get_plugin_info()

        try:
            # 动态导入因子类
            module = importlib.import_module(plugin_info.factor_module)
            factor_class = getattr(module, plugin_info.factor_class)

            # 合并配置
            final_config = plugin_info.default_config.copy()
            if config:
                final_config.update(config)

            # 过滤掉因子构造函数不需要的参数
            import inspect
            factor_init_signature = inspect.signature(factor_class.__init__)
            valid_params = set(factor_init_signature.parameters.keys()) - {'self'}

            # 只保留有效参数
            filtered_config = {k: v for k, v in final_config.items() if k in valid_params}

            # 创建因子实例
            if filtered_config:
                return factor_class(**filtered_config)
            else:
                return factor_class()

        except Exception as e:
            cls._get_logger().error(f"创建因子实例失败: {e}")
            return None
    
    @classmethod
    def validate_config(cls, config: Dict[str, Any]) -> bool:
        """验证配置参数"""
        try:
            # 基础验证逻辑
            plugin_info = cls.get_plugin_info()
            
            # 检查必需的配置项
            for key, value in config.items():
                if not isinstance(key, str):
                    return False
            
            return True
            
        except Exception:
            return False
    
    @classmethod
    def get_default_config(cls) -> Dict[str, Any]:
        """获取默认配置"""
        return cls.get_plugin_info().default_config.copy()
    
    @classmethod
    def load_config_from_file(cls, config_path: str) -> Dict[str, Any]:
        """从文件加载配置"""
        try:
            import tomllib
            from pathlib import Path
            
            config_file = Path(config_path)
            if not config_file.exists():
                return cls.get_default_config()
            
            if config_file.suffix.lower() == '.toml':
                with open(config_file, 'rb') as f:
                    config = tomllib.load(f)
            elif config_file.suffix.lower() in ['.yaml', '.yml']:
                import yaml
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)
            else:
                raise ValueError(f"不支持的配置文件格式: {config_file.suffix}")
            
            # 合并默认配置
            default_config = cls.get_default_config()
            default_config.update(config)
            
            return default_config
            
        except Exception as e:
            cls._get_logger().error(f"加载配置文件失败 {config_path}: {e}")
            return cls.get_default_config()
    
    @classmethod
    def _get_logger(cls):
        """获取日志器"""
        return get_logger(f'Plugin_{cls.get_plugin_info().name}')


class FactorRegistry:
    """因子注册表"""
    
    def __init__(self):
        self._factors = {}
        self.logger = get_logger('FactorRegistry')
    
    def register_factor(self, factor_name: str, factor_class, module_path: str):
        """注册因子"""
        self._factors[factor_name] = {
            'class': factor_class,
            'module': module_path
        }
        self.logger.debug(f"注册因子: {factor_name} -> {module_path}")
    
    def get_factor_class(self, factor_name: str):
        """获取因子类"""
        if factor_name in self._factors:
            return self._factors[factor_name]['class']
        return None
    
    def list_factors(self) -> List[str]:
        """列出所有注册的因子"""
        return list(self._factors.keys())
    
    def get_factor_info(self, factor_name: str) -> Dict[str, Any]:
        """获取因子信息"""
        return self._factors.get(factor_name, {})


# 全局因子注册表
_factor_registry = FactorRegistry()

def get_factor_registry() -> FactorRegistry:
    """获取全局因子注册表"""
    return _factor_registry
