#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试数据合并功能

测试历史日线数据与实时tick数据的合并逻辑
"""

import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, Optional

# 添加项目路径
sys.path.append('.')

from utils.logger import get_logger

logger = get_logger('DataMergeTest')


def generate_mock_historical_data(days: int = 100) -> pd.DataFrame:
    """生成模拟历史数据"""
    dates = pd.date_range(start=datetime.now() - timedelta(days=days), periods=days, freq='D')
    
    data = []
    base_price = 100.0
    current_price = base_price
    
    for date in dates:
        # 模拟价格随机游走
        change = np.random.normal(0, 0.02)
        current_price *= (1 + change)
        
        # 生成OHLC
        open_price = current_price * (1 + np.random.normal(0, 0.005))
        high = current_price * (1 + abs(np.random.normal(0, 0.01)))
        low = current_price * (1 - abs(np.random.normal(0, 0.01)))
        close = current_price
        volume = int(np.random.uniform(1000000, 5000000))
        
        data.append({
            'trade_time': date.strftime('%Y-%m-%d 00:00:00'),
            'open': round(open_price, 2),
            'high': round(high, 2),
            'low': round(low, 2),
            'close': round(close, 2),
            'volume': volume
        })
    
    df = pd.DataFrame(data)
    df['trade_time'] = pd.to_datetime(df['trade_time'])
    return df


def generate_mock_tick_data(base_price: float = 100.0) -> Dict:
    """生成模拟tick数据"""
    current_time = datetime.now()
    
    # 模拟当日开盘价和当前价格
    open_price = base_price * (1 + np.random.normal(0, 0.01))
    current_price = open_price * (1 + np.random.normal(0, 0.02))
    
    # 模拟当日高低价
    high_price = max(open_price, current_price) * (1 + abs(np.random.normal(0, 0.005)))
    low_price = min(open_price, current_price) * (1 - abs(np.random.normal(0, 0.005)))
    
    return {
        'trade_time': current_time,
        'open': round(open_price, 2),
        'high': round(high_price, 2),
        'low': round(low_price, 2),
        'price': round(current_price, 2),
        'volume': int(np.random.uniform(5000000, 20000000))
    }


def merge_historical_and_current_data(historical_data: pd.DataFrame, 
                                    current_tick: Optional[Dict]) -> pd.DataFrame:
    """合并历史数据和当前tick数据（复制盘中进程的逻辑）"""
    try:
        # 如果没有当前tick数据，直接返回历史数据
        if current_tick is None:
            logger.warning("没有当前tick数据，使用历史数据")
            return historical_data
        
        # 格式化当前tick的交易时间为0点格式
        current_time = pd.to_datetime(current_tick['trade_time'])
        current_date = current_time.strftime('%Y-%m-%d')
        formatted_time = pd.to_datetime(f"{current_date} 00:00:00")
        
        # 检查今日是否已有数据
        today_data = historical_data[historical_data['trade_time'].dt.date == current_time.date()]
        
        if len(today_data) > 0:
            # 更新今日数据
            today_idx = historical_data[historical_data['trade_time'].dt.date == current_time.date()].index[-1]
            
            logger.info(f"更新今日数据，原收盘价: {historical_data.loc[today_idx, 'close']}")
            
            # 更新当日的OHLC数据
            # 开盘价保持不变，收盘价更新为当前价格
            historical_data.loc[today_idx, 'close'] = current_tick['price']
            
            # 更新最高价和最低价（如果当前价格更极端）
            if current_tick['price'] > historical_data.loc[today_idx, 'high']:
                historical_data.loc[today_idx, 'high'] = current_tick['price']
            if current_tick['price'] < historical_data.loc[today_idx, 'low']:
                historical_data.loc[today_idx, 'low'] = current_tick['price']
            
            # 更新成交量（累计）
            historical_data.loc[today_idx, 'volume'] = current_tick['volume']
            
            logger.info(f"更新后收盘价: {current_tick['price']}")
            
        else:
            # 添加新的今日数据
            new_row = {
                'trade_time': formatted_time,
                'open': current_tick['open'],
                'high': current_tick['high'],
                'low': current_tick['low'],
                'close': current_tick['price'],
                'volume': current_tick['volume']
            }
            
            # 添加到DataFrame
            new_df = pd.DataFrame([new_row])
            historical_data = pd.concat([historical_data, new_df], ignore_index=True)
            historical_data = historical_data.sort_values('trade_time').reset_index(drop=True)
            
            logger.info(f"添加今日数据: 开盘 {current_tick['open']}, 当前 {current_tick['price']}")
        
        return historical_data
        
    except Exception as e:
        logger.error(f"合并历史和当前数据失败: {e}")
        return historical_data


def test_data_merge():
    """测试数据合并功能"""
    logger.info("🚀 开始测试数据合并功能")
    
    # 1. 生成模拟历史数据
    logger.info("📊 生成模拟历史数据...")
    historical_data = generate_mock_historical_data(100)
    logger.info(f"历史数据: {len(historical_data)} 条记录")
    logger.info(f"最新历史数据: {historical_data.iloc[-1]['trade_time']} - 收盘价: {historical_data.iloc[-1]['close']}")
    
    # 2. 生成模拟tick数据
    logger.info("\n📈 生成模拟tick数据...")
    last_close = historical_data.iloc[-1]['close']
    current_tick = generate_mock_tick_data(last_close)
    logger.info(f"当前tick数据: {current_tick}")
    
    # 3. 测试场景1：今日已有数据（更新模式）
    logger.info("\n🔄 测试场景1：今日已有数据（更新模式）")
    
    # 添加今日的初始数据
    today = datetime.now().strftime('%Y-%m-%d 00:00:00')
    today_initial = {
        'trade_time': pd.to_datetime(today),
        'open': current_tick['open'],
        'high': current_tick['open'] * 1.01,
        'low': current_tick['open'] * 0.99,
        'close': current_tick['open'] * 1.005,
        'volume': 1000000
    }
    
    test_data_1 = pd.concat([historical_data, pd.DataFrame([today_initial])], ignore_index=True)
    logger.info(f"添加今日初始数据后: {len(test_data_1)} 条记录")
    
    # 合并数据
    merged_data_1 = merge_historical_and_current_data(test_data_1, current_tick)
    logger.info(f"合并后数据: {len(merged_data_1)} 条记录")
    logger.info(f"最新数据: {merged_data_1.iloc[-1]}")
    
    # 4. 测试场景2：今日无数据（新增模式）
    logger.info("\n➕ 测试场景2：今日无数据（新增模式）")
    
    # 使用原始历史数据（不包含今日）
    merged_data_2 = merge_historical_and_current_data(historical_data, current_tick)
    logger.info(f"合并后数据: {len(merged_data_2)} 条记录")
    logger.info(f"最新数据: {merged_data_2.iloc[-1]}")
    
    # 5. 测试场景3：无tick数据
    logger.info("\n❌ 测试场景3：无tick数据")
    merged_data_3 = merge_historical_and_current_data(historical_data, None)
    logger.info(f"无tick数据时: {len(merged_data_3)} 条记录（应该与原始数据相同）")
    
    # 6. 验证数据完整性
    logger.info("\n✅ 验证数据完整性")
    
    def validate_data(df: pd.DataFrame, name: str):
        logger.info(f"\n{name} 验证:")
        logger.info(f"  数据量: {len(df)} 条")
        logger.info(f"  时间范围: {df['trade_time'].min()} 到 {df['trade_time'].max()}")
        logger.info(f"  价格范围: {df['close'].min():.2f} - {df['close'].max():.2f}")
        logger.info(f"  成交量范围: {df['volume'].min():,} - {df['volume'].max():,}")
        
        # 检查数据类型
        logger.info(f"  数据类型: {df.dtypes.to_dict()}")
        
        # 检查缺失值
        missing = df.isnull().sum()
        if missing.any():
            logger.warning(f"  缺失值: {missing.to_dict()}")
        else:
            logger.info("  ✅ 无缺失值")
    
    validate_data(merged_data_1, "场景1（更新模式）")
    validate_data(merged_data_2, "场景2（新增模式）")
    validate_data(merged_data_3, "场景3（无tick数据）")
    
    logger.info("\n🎉 数据合并功能测试完成！")


if __name__ == "__main__":
    test_data_merge()
