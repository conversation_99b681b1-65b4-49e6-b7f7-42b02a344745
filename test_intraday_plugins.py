#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
盘中监控插件系统测试脚本

测试专为 intraday_stock_monitor_modular.py 设计的插件系统
"""

import sys
import os
import pandas as pd
import numpy as np
import time
from datetime import datetime, timedelta

# 添加项目路径
sys.path.append('.')

from plugins import get_plugin_manager, get_config_manager
from factors import FactorManager
from utils.logger import get_logger

logger = get_logger('IntradayPluginsTest')


def generate_test_data(days: int = 150) -> pd.DataFrame:
    """生成测试数据"""
    dates = pd.date_range(start=datetime.now() - timedelta(days=days), periods=days, freq='D')
    
    # 生成模拟的OHLCV数据
    np.random.seed(42)
    base_price = 100.0
    
    data = []
    current_price = base_price
    
    for i, date in enumerate(dates):
        # 模拟价格随机游走
        change = np.random.normal(0, 0.02)
        current_price *= (1 + change)
        
        # 生成OHLC
        high = current_price * (1 + abs(np.random.normal(0, 0.01)))
        low = current_price * (1 - abs(np.random.normal(0, 0.01)))
        open_price = current_price * (1 + np.random.normal(0, 0.005))
        close = current_price
        
        # 生成成交量（偶尔有激增）
        base_volume = 1000000
        if np.random.random() < 0.1:  # 10%概率成交量激增
            volume = base_volume * np.random.uniform(3, 8)
        else:
            volume = base_volume * np.random.uniform(0.5, 2)
        
        data.append({
            'trade_time': date,
            'open': open_price,
            'high': high,
            'low': low,
            'close': close,
            'volume': int(volume)
        })
    
    return pd.DataFrame(data)


def test_intraday_plugin_discovery():
    """测试盘中监控插件发现"""
    logger.info("🔍 测试盘中监控插件发现...")
    
    plugin_manager = get_plugin_manager()
    
    # 获取统计信息
    stats = plugin_manager.get_statistics()
    logger.info(f"插件统计:")
    logger.info(f"  总插件数: {stats['total_plugins']}")
    logger.info(f"  启用插件数: {stats['enabled_plugins']}")
    logger.info(f"  已注册因子数: {stats['loaded_factors']}")
    logger.info(f"  可用场景: {', '.join(stats['available_scenarios'])}")
    
    # 列出所有插件
    plugins = plugin_manager.list_plugins()
    logger.info(f"\n📋 发现的插件列表:")
    
    expected_plugins = [
        'ema_factor',
        'bollinger_factor', 
        'fibonacci_factor',
        'volume_factor',
        'macd_divergence_factor',
        'rsi_divergence_factor',
        'kdj_divergence_factor',
        'ema_turnaround_composite_factor'
    ]
    
    found_plugins = []
    for name, info in plugins.items():
        found_plugins.append(name)
        logger.info(f"  ✅ {name} v{info.version} ({info.category})")
        logger.info(f"     因子类: {info.factor_class} @ {info.factor_module}")
        logger.info(f"     场景: {', '.join(info.scenarios)}")
        logger.info(f"     优先级: {info.priority}, 计算成本: {info.computational_cost}")
        logger.info("")
    
    # 检查是否找到了所有期望的插件
    missing_plugins = set(expected_plugins) - set(found_plugins)
    if missing_plugins:
        logger.warning(f"⚠️ 缺少插件: {', '.join(missing_plugins)}")
        return False
    
    logger.info(f"✅ 所有期望的插件都已找到！")
    return len(plugins) >= len(expected_plugins)


def test_intraday_scenario_factors():
    """测试盘中监控场景的因子加载"""
    logger.info("🔧 测试盘中监控场景因子加载...")
    
    plugin_manager = get_plugin_manager()
    config_manager = get_config_manager()
    
    # 测试盘中监控场景
    scenario = 'intraday'
    logger.info(f"\n测试场景: {scenario}")
    
    # 获取适用的插件
    suitable_plugins = plugin_manager.get_plugins_by_scenario(scenario)
    logger.info(f"  适用插件: {suitable_plugins}")
    
    # 期望的盘中监控插件
    expected_intraday_plugins = [
        'ema_factor',
        'bollinger_factor',
        'fibonacci_factor', 
        'volume_factor',
        'macd_divergence_factor',
        'rsi_divergence_factor',
        'kdj_divergence_factor'
    ]
    
    # 检查是否包含所有期望的插件
    missing_plugins = set(expected_intraday_plugins) - set(suitable_plugins)
    if missing_plugins:
        logger.warning(f"⚠️ 盘中监控场景缺少插件: {', '.join(missing_plugins)}")
    
    # 创建因子实例
    created_factors = []
    for plugin_name in suitable_plugins:
        try:
            # 获取配置
            config = config_manager.get_factor_config(plugin_name, scenario)
            
            # 创建因子实例
            factor = plugin_manager.create_factor_instance(plugin_name, config)
            if factor:
                created_factors.append((plugin_name, factor))
                logger.info(f"    ✅ {plugin_name}: {factor.__class__.__name__}")
                logger.info(f"       数据要求: ≥{factor.min_data_length} 行")
                logger.info(f"       列要求: {factor.required_columns}")
            else:
                logger.warning(f"    ❌ {plugin_name}: 创建失败")
                
        except Exception as e:
            logger.error(f"    💥 {plugin_name}: {e}")
    
    logger.info(f"  成功创建 {len(created_factors)} 个因子")
    return len(created_factors) >= 5  # 至少要有5个核心因子


def test_intraday_factor_calculation():
    """测试盘中监控因子计算"""
    logger.info("🧮 测试盘中监控因子计算...")
    
    # 生成测试数据
    test_data = generate_test_data(200)
    logger.info(f"生成测试数据: {len(test_data)} 行")
    
    plugin_manager = get_plugin_manager()
    config_manager = get_config_manager()
    
    # 为intraday场景创建因子
    scenario = 'intraday'
    suitable_plugins = plugin_manager.get_plugins_by_scenario(scenario)
    
    if not suitable_plugins:
        logger.error("没有找到适用的插件")
        return False
    
    logger.info(f"测试 {len(suitable_plugins)} 个因子的计算...")
    
    results = {}
    calculation_times = {}
    
    for plugin_name in suitable_plugins:
        try:
            start_time = time.time()
            
            # 获取配置并创建因子
            config = config_manager.get_factor_config(plugin_name, scenario)
            factor = plugin_manager.create_factor_instance(plugin_name, config)
            
            if not factor:
                logger.warning(f"  ❌ {plugin_name}: 因子创建失败")
                continue
            
            # 计算因子
            result = factor.calculate_with_validation(test_data)
            
            end_time = time.time()
            calculation_time = end_time - start_time
            
            calculation_times[plugin_name] = calculation_time
            
            if result:
                results[plugin_name] = result
                logger.info(f"  ✅ {plugin_name}: {result.factor_value:.4f} "
                          f"(置信度: {result.confidence:.4f}, "
                          f"信号强度: {result.signal_strength}, "
                          f"耗时: {calculation_time:.3f}s)")
                
                # 显示元数据信息
                if result.metadata:
                    key_metadata = {}
                    for key, value in result.metadata.items():
                        if isinstance(value, (int, float, str, bool)):
                            key_metadata[key] = value
                    if key_metadata:
                        logger.debug(f"     元数据: {key_metadata}")
            else:
                logger.warning(f"  ❌ {plugin_name}: 计算返回None")
                
        except Exception as e:
            logger.error(f"  💥 {plugin_name}: 计算异常 - {e}")
    
    # 性能统计
    if calculation_times:
        avg_time = np.mean(list(calculation_times.values()))
        max_time = max(calculation_times.values())
        min_time = min(calculation_times.values())
        
        logger.info(f"\n📊 性能统计:")
        logger.info(f"  平均计算时间: {avg_time:.3f}s")
        logger.info(f"  最大计算时间: {max_time:.3f}s")
        logger.info(f"  最小计算时间: {min_time:.3f}s")
        logger.info(f"  成功率: {len(results)}/{len(suitable_plugins)} ({len(results)/len(suitable_plugins)*100:.1f}%)")
        
        # 检查性能要求（盘中监控要求低延迟）
        if avg_time > 1.0:
            logger.warning(f"⚠️ 平均计算时间过长: {avg_time:.3f}s > 1.0s")
        if max_time > 5.0:
            logger.warning(f"⚠️ 最大计算时间过长: {max_time:.3f}s > 5.0s")
    
    return len(results) >= 5  # 至少要有5个因子成功计算


def test_factor_manager_integration():
    """测试与FactorManager的集成（模拟盘中监控流程）"""
    logger.info("🔗 测试FactorManager集成（模拟盘中监控）...")
    
    plugin_manager = get_plugin_manager()
    config_manager = get_config_manager()
    factor_manager = FactorManager()
    
    # 为盘中监控场景加载因子到FactorManager
    scenario = 'intraday'
    suitable_plugins = plugin_manager.get_plugins_by_scenario(scenario)
    
    loaded_count = 0
    for plugin_name in suitable_plugins:
        try:
            config = config_manager.get_factor_config(plugin_name, scenario)
            factor = plugin_manager.create_factor_instance(plugin_name, config)
            
            if factor:
                factor_manager.register_factor(factor)
                loaded_count += 1
                logger.info(f"  注册因子: {plugin_name}")
                
        except Exception as e:
            logger.error(f"  注册因子失败 {plugin_name}: {e}")
    
    logger.info(f"成功注册 {loaded_count} 个因子到FactorManager")
    
    # 测试批量计算（模拟盘中监控的核心流程）
    test_data = generate_test_data(150)
    
    try:
        start_time = time.time()
        all_results = factor_manager.calculate_all_factors(
            test_data,
            stock_code="000001.SZ",
            current_price=test_data['close'].iloc[-1]
        )
        end_time = time.time()
        
        logger.info(f"批量计算完成:")
        logger.info(f"  计算因子数: {len(all_results)}")
        logger.info(f"  总耗时: {end_time - start_time:.3f}s")
        
        if len(all_results) > 0:
            logger.info(f"  平均每因子: {(end_time - start_time)/len(all_results):.3f}s")
            
            # 显示因子结果
            for factor_name, result in all_results.items():
                if result and result.signal_strength != "NONE":
                    logger.info(f"  📊 {factor_name}: {result.factor_value:.4f} "
                              f"({result.signal_strength})")
        
        # 检查盘中监控性能要求
        total_time = end_time - start_time
        if total_time > 10.0:
            logger.warning(f"⚠️ 总计算时间过长: {total_time:.3f}s > 10.0s")
        
        return len(all_results) >= 5
        
    except Exception as e:
        logger.error(f"批量计算失败: {e}")
        return False


def main():
    """主测试函数"""
    logger.info("🚀 开始盘中监控插件系统测试")
    logger.info("=" * 60)
    
    tests = [
        ("插件发现", test_intraday_plugin_discovery),
        ("场景因子加载", test_intraday_scenario_factors),
        ("因子计算", test_intraday_factor_calculation),
        ("FactorManager集成", test_factor_manager_integration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                logger.info(f"✅ {test_name} 测试通过")
                passed += 1
            else:
                logger.error(f"❌ {test_name} 测试失败")
        except Exception as e:
            logger.error(f"💥 {test_name} 测试异常: {e}")
            import traceback
            logger.error(traceback.format_exc())
    
    logger.info("\n" + "=" * 60)
    logger.info(f"🏁 测试完成: {passed}/{total} 通过")
    
    if passed == total:
        logger.info("🎉 所有测试通过！盘中监控插件系统工作正常。")
        return True
    else:
        logger.error(f"⚠️ 有 {total - passed} 个测试失败，请检查系统配置。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
