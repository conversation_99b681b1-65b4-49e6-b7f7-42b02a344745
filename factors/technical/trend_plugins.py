#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
趋势类技术指标因子插件

包含EMA、SMA、MACD等趋势相关的因子插件
"""

import numpy as np
import pandas as pd
from typing import Dict, Any, Optional, List, Tuple
import talib

from ..core.base_plugin import AdvancedFactorPlugin, FactorPluginInfo, VectorizedFactorMixin
from ..base_factor import BaseFactor, FactorResult
from ..core.config_manager import get_config_manager
from indicators.talib_wrapper import calculate_ema_series


class EmaFactorPlugin(AdvancedFactorPlugin, VectorizedFactorMixin):
    """EMA因子插件"""
    
    @classmethod
    def get_plugin_info(cls) -> FactorPluginInfo:
        return FactorPluginInfo(
            name="ema_factor",
            version="2.0.0",
            description="EMA均线因子，检测价格是否接近关键EMA均线，支持向量化计算和参数优化",
            author="QuantFM Team",
            category="technical",
            dependencies=["close"],
            scenarios=["intraday", "swing", "position"],
            computational_cost="low",
            priority="high",
            tags=["trend", "ema", "moving_average"],
            
            # 高级特性
            qlib_compatible=True,
            trainable=False,
            optimizable=True,
            vectorized=True,
            
            # 性能指标
            min_data_length=50,
            max_lookback=676,
            memory_usage="low",
            
            # 配置模式
            config_schema={
                'periods': 'list',
                'price_threshold': 'float',
                'weight_decay': 'float',
                'min_periods': 'int'
            },
            default_config={
                'periods': [12, 62, 144, 169, 377, 576, 676],
                'price_threshold': 0.005,
                'weight_decay': 0.1,
                'min_periods': 3
            }
        )
    
    @classmethod
    def create_factor(cls, config: Dict[str, Any] = None) -> BaseFactor:
        config = config or cls.get_default_config()
        return EmaFactor(
            periods=config.get('periods', [12, 62, 144, 169, 377, 576, 676]),
            price_threshold=config.get('price_threshold', 0.005),
            weight_decay=config.get('weight_decay', 0.1),
            min_periods=config.get('min_periods', 3)
        )
    
    @classmethod
    def optimize_parameters(cls, data: pd.DataFrame, 
                          param_ranges: Dict[str, Any] = None,
                          objective_func: callable = None,
                          method: str = 'grid_search',
                          **kwargs) -> Dict[str, Any]:
        """EMA因子参数优化"""
        if param_ranges is None:
            param_ranges = {
                'price_threshold': [0.003, 0.005, 0.007, 0.01],
                'weight_decay': [0.05, 0.1, 0.15, 0.2],
                'min_periods': [2, 3, 4, 5]
            }
        
        return super().optimize_parameters(data, param_ranges, objective_func, method, **kwargs)


class EmaFactor(BaseFactor, VectorizedFactorMixin):
    """EMA因子实现"""
    
    def __init__(self, periods: List[int] = None, price_threshold: float = 0.005,
                 weight_decay: float = 0.1, min_periods: int = 3):
        super().__init__("ema_factor", "technical")
        self.periods = periods or [12, 62, 144, 169, 377, 576, 676]
        self.price_threshold = price_threshold
        self.weight_decay = weight_decay
        self.min_periods = min_periods
        
        # 计算周期权重
        self.period_weights = self._calculate_period_weights()
        
        # 使用更合理的最小数据长度：最大周期 + 50个缓冲
        self.min_data_length = max(self.periods) + 50
        self.required_columns = ['close']
    
    def _calculate_period_weights(self) -> Dict[int, float]:
        """计算周期权重（短期权重更高）"""
        weights = {}
        for i, period in enumerate(sorted(self.periods)):
            # 指数衰减权重
            weight = np.exp(-i * self.weight_decay)
            weights[period] = weight
        
        # 归一化
        total_weight = sum(weights.values())
        return {k: v / total_weight for k, v in weights.items()}
    
    def calculate(self, data: pd.DataFrame, **kwargs) -> Optional[FactorResult]:
        """计算EMA因子 - 使用indicators中的高效EMA计算"""
        try:
            current_price = float(data['close'].iloc[-1])

            # 使用indicators中的批量EMA计算
            try:
                ema_data = calculate_ema_series(data, self.periods, 'close')
                use_advanced = True
            except Exception as e:
                self.logger.warning(f"高级EMA计算失败，使用简化版本: {e}")
                use_advanced = False

            # 向量化计算所有EMA
            ema_values = {}
            near_emas = []

            for period in self.periods:
                # 更宽松的数据长度检查：只需要周期长度 + 10个缓冲
                if len(data) < period + 10:
                    continue

                if use_advanced:
                    # 使用高级EMA计算结果
                    ema_col = f'ema_{period}'
                    if ema_col in ema_data.columns:
                        current_ema = ema_data[ema_col].iloc[-1]
                    else:
                        continue
                else:
                    # 降级到向量化EMA计算
                    close_prices = data['close'].values
                    ema_array = self.vectorized_ema(close_prices, period)
                    current_ema = ema_array[-1]

                if np.isnan(current_ema) or current_ema <= 0:
                    continue

                ema_values[f'ema_{period}'] = current_ema

                # 检测价格是否接近EMA
                deviation = abs(current_price - current_ema) / current_ema
                if deviation <= self.price_threshold:
                    near_emas.append({
                        'period': period,
                        'value': current_ema,
                        'deviation': deviation,
                        'weight': self.period_weights.get(period, 0.1),
                        'proximity_score': 1 - deviation / self.price_threshold
                    })
            
            if len(near_emas) < self.min_periods:
                return FactorResult(
                    factor_name=self.factor_name,
                    factor_value=0.0,
                    factor_type=self.factor_type,
                    confidence=0.0,
                    signal_strength="NONE",
                    metadata={
                        'ema_values': ema_values,
                        'near_emas_count': len(near_emas),
                        'min_periods_required': self.min_periods,
                        'current_price': current_price
                    }
                )
            
            # 计算加权因子值
            weighted_score = sum(
                ema['proximity_score'] * ema['weight'] 
                for ema in near_emas
            )
            
            # 归一化到[0,1]
            factor_value = min(1.0, weighted_score)
            
            # 计算置信度（考虑接近的EMA数量和权重分布）
            confidence = min(1.0, factor_value * (1 + len(near_emas) * 0.1))
            
            # 确定信号强度
            if factor_value > 0.8 and len(near_emas) >= 3:
                signal_strength = "STRONG"
            elif factor_value > 0.6 and len(near_emas) >= 2:
                signal_strength = "MEDIUM"
            elif factor_value > 0.3:
                signal_strength = "WEAK"
            else:
                signal_strength = "NONE"
            
            return FactorResult(
                factor_name=self.factor_name,
                factor_value=factor_value,
                factor_type=self.factor_type,
                confidence=confidence,
                signal_strength=signal_strength,
                metadata={
                    'ema_values': ema_values,
                    'near_emas': near_emas,
                    'weighted_score': weighted_score,
                    'current_price': current_price,
                    'period_weights': self.period_weights
                }
            )
            
        except Exception as e:
            self.logger.error(f"EMA因子计算失败: {e}")
            return None


class MacdFactorPlugin(AdvancedFactorPlugin, VectorizedFactorMixin):
    """MACD因子插件"""
    
    @classmethod
    def get_plugin_info(cls) -> FactorPluginInfo:
        return FactorPluginInfo(
            name="macd_factor",
            version="2.0.0",
            description="MACD因子，检测MACD金叉死叉和背离信号",
            author="QuantFM Team",
            category="technical",
            dependencies=["close"],
            scenarios=["intraday", "swing", "after_market"],
            computational_cost="medium",
            priority="high",
            tags=["trend", "macd", "momentum"],
            
            # 高级特性
            qlib_compatible=True,
            trainable=True,
            optimizable=True,
            vectorized=True,
            
            # 性能指标
            min_data_length=100,
            max_lookback=200,
            memory_usage="medium",
            
            # 配置模式
            config_schema={
                'fast_period': 'int',
                'slow_period': 'int',
                'signal_period': 'int',
                'divergence_lookback': 'int',
                'cross_threshold': 'float'
            },
            default_config={
                'fast_period': 12,
                'slow_period': 26,
                'signal_period': 9,
                'divergence_lookback': 20,
                'cross_threshold': 0.0001
            }
        )
    
    @classmethod
    def create_factor(cls, config: Dict[str, Any] = None) -> BaseFactor:
        config = config or cls.get_default_config()
        return MacdFactor(
            fast_period=config.get('fast_period', 12),
            slow_period=config.get('slow_period', 26),
            signal_period=config.get('signal_period', 9),
            divergence_lookback=config.get('divergence_lookback', 20),
            cross_threshold=config.get('cross_threshold', 0.0001)
        )


class MacdFactor(BaseFactor, VectorizedFactorMixin):
    """MACD因子实现 - 使用divergence中的高级MACD背离方法"""

    def __init__(self, fast_period: int = 12, slow_period: int = 26,
                 signal_period: int = 9, divergence_lookback: int = 20,
                 cross_threshold: float = 0.0001):
        super().__init__("macd_factor", "technical")
        self.fast_period = fast_period
        self.slow_period = slow_period
        self.signal_period = signal_period
        self.divergence_lookback = divergence_lookback
        self.cross_threshold = cross_threshold

        # 导入并初始化MACD背离因子
        try:
            from ..divergence.macd_divergence import MACDDivergenceFactor, MACDDivergenceConfig

            # 创建配置
            config = MACDDivergenceConfig(
                fastperiod=fast_period,
                slowperiod=slow_period,
                signalperiod=signal_period,
                window=5,  # 使用正确的参数名
                min_bars_between=divergence_lookback,
                output_mode="boolean"  # 使用布尔信号模式
            )

            self.macd_divergence = MACDDivergenceFactor(config)
            self.use_advanced = True

        except ImportError:
            self.logger.warning("无法导入MACD背离因子，使用简化版本")
            self.use_advanced = False

        self.min_data_length = max(slow_period, signal_period) * 3
        self.required_columns = ['close']
    
    def calculate(self, data: pd.DataFrame, **kwargs) -> Optional[FactorResult]:
        """计算MACD因子 - 使用高级MACD背离方法"""
        try:
            if self.use_advanced:
                # 使用高级MACD背离因子
                result = self.macd_divergence.calculate(data, **kwargs)

                if result is None:
                    return None

                # 从高级结果中提取信号
                return self._extract_signals_from_advanced_result(result, data)
            else:
                # 降级到简化版本
                return self._calculate_simple_macd(data, **kwargs)

        except Exception as e:
            self.logger.error(f"MACD因子计算失败: {e}")
            return None

    def _extract_signals_from_advanced_result(self, result, data: pd.DataFrame) -> Optional[FactorResult]:
        """从高级MACD背离结果中提取信号"""
        try:
            if hasattr(result, 'factor_value'):
                # 如果返回的是FactorResult对象
                return FactorResult(
                    factor_name=self.factor_name,
                    factor_value=result.factor_value,
                    factor_type=self.factor_type,
                    confidence=result.confidence,
                    signal_strength=result.signal_strength,
                    metadata=result.metadata
                )
            else:
                # 如果返回的是DataFrame，提取最后一行的信号
                last_row = result.iloc[-1]

                # 计算综合因子值
                factor_value = 0.0
                signals = []

                # 检查背离信号
                if last_row.get('macd_bullish_divergence', False):
                    factor_value += 0.8
                    signals.append('bullish_divergence')

                if last_row.get('macd_bearish_divergence', False):
                    factor_value += 0.6  # 熊市背离信号稍弱
                    signals.append('bearish_divergence')

                # 检查MACD金叉死叉
                if last_row.get('macd_golden_cross', False):
                    factor_value += 0.6
                    signals.append('golden_cross')

                if last_row.get('macd_death_cross', False):
                    factor_value += 0.4
                    signals.append('death_cross')

                # 检查MACD位置信号
                macd_value = last_row.get('macd', 0)
                signal_value = last_row.get('macd_signal', 0)

                if macd_value > signal_value and macd_value > 0:
                    factor_value += 0.2
                    signals.append('bullish_position')
                elif macd_value < signal_value and macd_value < 0:
                    factor_value += 0.1
                    signals.append('bearish_position')

                factor_value = min(1.0, factor_value)

                # 计算置信度
                histogram = last_row.get('macd_histogram', 0)
                confidence = min(1.0, abs(histogram) * 100 + 0.3)

                # 确定信号强度
                if factor_value > 0.8:
                    signal_strength = "STRONG"
                elif factor_value > 0.6:
                    signal_strength = "MEDIUM"
                elif factor_value > 0.3:
                    signal_strength = "WEAK"
                else:
                    signal_strength = "NONE"

                return FactorResult(
                    factor_name=self.factor_name,
                    factor_value=factor_value,
                    factor_type=self.factor_type,
                    confidence=confidence,
                    signal_strength=signal_strength,
                    metadata={
                        'signals': signals,
                        'macd': macd_value,
                        'macd_signal': signal_value,
                        'macd_histogram': histogram,
                        'divergence_strength': last_row.get('divergence_strength', 0)
                    }
                )

        except Exception as e:
            self.logger.error(f"提取高级MACD信号失败: {e}")
            return None

    def _calculate_simple_macd(self, data: pd.DataFrame, **kwargs) -> Optional[FactorResult]:
        """简化版MACD计算（降级方案）"""
        try:
            close_prices = data['close'].values

            # 使用talib计算MACD
            macd_line, signal_line, histogram = talib.MACD(
                close_prices,
                fastperiod=self.fast_period,
                slowperiod=self.slow_period,
                signalperiod=self.signal_period
            )

            if len(macd_line) < 2 or np.isnan(macd_line[-1]):
                return None

            current_macd = macd_line[-1]
            current_signal = signal_line[-1]
            current_histogram = histogram[-1]
            prev_histogram = histogram[-2]

            # 检测金叉死叉
            cross_signal = self._detect_cross_signal(histogram)

            # 简单的背离检测
            divergence_signal = self._detect_simple_divergence(
                data['close'].iloc[-self.divergence_lookback:],
                macd_line[-self.divergence_lookback:]
            )

            # 计算因子值
            factor_value = 0.0

            # 金叉信号
            if cross_signal == 'golden_cross':
                factor_value += 0.6
            elif cross_signal == 'death_cross':
                factor_value += 0.4

            # 背离信号
            if divergence_signal == 'bullish_divergence':
                factor_value += 0.4
            elif divergence_signal == 'bearish_divergence':
                factor_value += 0.3

            # MACD位置信号
            if current_macd > current_signal and current_macd > 0:
                factor_value += 0.2
            elif current_macd < current_signal and current_macd < 0:
                factor_value += 0.1

            factor_value = min(1.0, factor_value)

            # 计算置信度
            confidence = min(1.0, abs(current_histogram) * 100 + 0.3)

            # 确定信号强度
            if factor_value > 0.8:
                signal_strength = "STRONG"
            elif factor_value > 0.6:
                signal_strength = "MEDIUM"
            elif factor_value > 0.3:
                signal_strength = "WEAK"
            else:
                signal_strength = "NONE"

            return FactorResult(
                factor_name=self.factor_name,
                factor_value=factor_value,
                factor_type=self.factor_type,
                confidence=confidence,
                signal_strength=signal_strength,
                metadata={
                    'current_macd': current_macd,
                    'current_signal': current_signal,
                    'current_histogram': current_histogram,
                    'cross_signal': cross_signal,
                    'divergence_signal': divergence_signal,
                    'method': 'simple'
                }
            )

        except Exception as e:
            self.logger.error(f"简化MACD计算失败: {e}")
            return None
    
    def _detect_cross_signal(self, histogram: np.ndarray) -> str:
        """检测金叉死叉信号"""
        if len(histogram) < 2:
            return 'none'

        current = histogram[-1]
        previous = histogram[-2]

        # 金叉：从负转正
        if previous <= -self.cross_threshold and current >= self.cross_threshold:
            return 'golden_cross'

        # 死叉：从正转负
        if previous >= self.cross_threshold and current <= -self.cross_threshold:
            return 'death_cross'

        return 'none'

    def _detect_simple_divergence(self, prices: pd.Series, macd_values: np.ndarray) -> str:
        """简单的背离检测信号"""
        try:
            if len(prices) < 10 or len(macd_values) < 10:
                return 'none'

            # 简化的背离检测
            price_trend = np.polyfit(range(len(prices)), prices.values, 1)[0]
            macd_trend = np.polyfit(range(len(macd_values)), macd_values, 1)[0]

            # 牛市背离：价格下跌，MACD上升
            if price_trend < -0.001 and macd_trend > 0.001:
                return 'bullish_divergence'

            # 熊市背离：价格上涨，MACD下跌
            if price_trend > 0.001 and macd_trend < -0.001:
                return 'bearish_divergence'

            return 'none'

        except Exception:
            return 'none'


class EmaTurnaroundFactorPlugin(AdvancedFactorPlugin, VectorizedFactorMixin):
    """EMA拐头因子插件"""

    @classmethod
    def get_plugin_info(cls) -> FactorPluginInfo:
        return FactorPluginInfo(
            name="ema_turnaround_factor",
            version="2.0.0",
            description="EMA拐头因子，检测EMA均线的拐头向上信号",
            author="QuantFM Team",
            category="technical",
            dependencies=["close"],
            scenarios=["after_market", "research"],  # 不适用于高频盘中监控
            computational_cost="medium",
            priority="medium",
            tags=["trend_change", "ema", "turnaround"],

            # 高级特性
            qlib_compatible=True,
            trainable=True,
            optimizable=True,
            vectorized=True,

            # 性能指标
            min_data_length=100,
            max_lookback=50,
            memory_usage="medium",

            # 配置模式
            config_schema={
                'target_period': 'int',
                'lookback_days': 'int',
                'price_threshold': 'float',
                'slope_threshold': 'float'
            },
            default_config={
                'target_period': 12,
                'lookback_days': 5,
                'price_threshold': 0.02,
                'slope_threshold': 0.001
            }
        )

    @classmethod
    def create_factor(cls, config: Dict[str, Any] = None) -> BaseFactor:
        config = config or cls.get_default_config()
        return EmaTurnaroundFactor(
            target_period=config.get('target_period', 12),
            lookback_days=config.get('lookback_days', 5),
            price_threshold=config.get('price_threshold', 0.02),
            slope_threshold=config.get('slope_threshold', 0.001)
        )


class EmaTurnaroundFactor(BaseFactor, VectorizedFactorMixin):
    """EMA拐头因子实现"""

    def __init__(self, target_period: int = 12, lookback_days: int = 5,
                 price_threshold: float = 0.02, slope_threshold: float = 0.001):
        super().__init__("ema_turnaround_factor", "technical")
        self.target_period = target_period
        self.lookback_days = lookback_days
        self.price_threshold = price_threshold
        self.slope_threshold = slope_threshold

        self.min_data_length = target_period * 2 + lookback_days
        self.required_columns = ['close']

    def calculate(self, data: pd.DataFrame, **kwargs) -> Optional[FactorResult]:
        """计算EMA拐头因子"""
        try:
            close_prices = data['close'].values
            current_price = close_prices[-1]

            # 计算EMA
            ema_array = self.vectorized_ema(close_prices, self.target_period)
            current_ema = ema_array[-1]

            if np.isnan(current_ema):
                return None

            # 检测EMA拐头
            turnaround_result = self._detect_ema_turnaround(ema_array)

            # 检查价格是否接近EMA
            price_near_ema = False
            if current_ema > 0:
                deviation = abs(current_price - current_ema) / current_ema
                price_near_ema = deviation <= self.price_threshold

            # 计算因子值
            factor_value = 0.0
            if turnaround_result['is_turnaround'] and price_near_ema:
                factor_value = turnaround_result['strength'] * 0.8 + 0.2
            elif turnaround_result['is_turnaround']:
                factor_value = turnaround_result['strength'] * 0.5
            elif price_near_ema:
                factor_value = 0.3

            # 计算置信度
            confidence = factor_value

            # 确定信号强度
            if factor_value > 0.7:
                signal_strength = "STRONG"
            elif factor_value > 0.5:
                signal_strength = "MEDIUM"
            elif factor_value > 0.2:
                signal_strength = "WEAK"
            else:
                signal_strength = "NONE"

            return FactorResult(
                factor_name=self.factor_name,
                factor_value=factor_value,
                factor_type=self.factor_type,
                confidence=confidence,
                signal_strength=signal_strength,
                metadata={
                    'current_ema': current_ema,
                    'current_price': current_price,
                    'turnaround_result': turnaround_result,
                    'price_near_ema': price_near_ema,
                    'price_deviation': abs(current_price - current_ema) / current_ema if current_ema > 0 else 0
                }
            )

        except Exception as e:
            self.logger.error(f"EMA拐头因子计算失败: {e}")
            return None

    def _detect_ema_turnaround(self, ema_array: np.ndarray) -> Dict[str, Any]:
        """检测EMA拐头"""
        try:
            if len(ema_array) < self.lookback_days + 1:
                return {'is_turnaround': False, 'strength': 0.0}

            # 计算EMA斜率
            recent_ema = ema_array[-self.lookback_days-1:]
            slopes = np.diff(recent_ema)

            if len(slopes) < 3:
                return {'is_turnaround': False, 'strength': 0.0}

            # 检查最近的斜率是否为正（上升）
            recent_slope = slopes[-1]
            prev_slopes = slopes[-3:-1]

            # 拐头条件：前面下降或平缓，现在明显上升
            is_turnaround = (recent_slope > self.slope_threshold and
                           (prev_slopes <= 0).any())

            if is_turnaround:
                # 计算拐头强度
                strength = min(1.0, abs(recent_slope) / (recent_ema[-1] * 0.01))
            else:
                strength = 0.0

            return {
                'is_turnaround': is_turnaround,
                'strength': strength,
                'recent_slope': recent_slope,
                'slopes': slopes.tolist()
            }

        except Exception:
            return {'is_turnaround': False, 'strength': 0.0}
