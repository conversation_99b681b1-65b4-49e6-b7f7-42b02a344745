#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
支撑阻力类技术指标因子插件

包含斐波那契、布林带、关键价位等支撑阻力相关的因子插件
"""

import numpy as np
import pandas as pd
from typing import Dict, Any, Optional, List, Tuple
import talib

from ..core.base_plugin import AdvancedFactorPlugin, FactorPluginInfo, VectorizedFactorMixin
from ..base_factor import BaseFactor, FactorResult
from indicators.fibonacci_comprehensive import calculate_fibonacci_levels_enhanced
# 布林带计算将使用内置方法


class FibonacciFactorPlugin(AdvancedFactorPlugin, VectorizedFactorMixin):
    """斐波那契因子插件"""
    
    @classmethod
    def get_plugin_info(cls) -> FactorPluginInfo:
        return FactorPluginInfo(
            name="fibonacci_factor",
            version="2.0.0",
            description="斐波那契回撤位因子，检测价格是否接近关键斐波那契位，支持向量化计算",
            author="QuantFM Team",
            category="technical",
            dependencies=["close", "high", "low"],
            scenarios=["intraday", "swing", "position"],
            computational_cost="medium",
            priority="high",
            tags=["support_resistance", "fibonacci", "retracement"],
            
            # 高级特性
            qlib_compatible=True,
            trainable=False,
            optimizable=True,
            vectorized=True,
            
            # 性能指标
            min_data_length=100,
            max_lookback=252,
            memory_usage="medium",
            
            # 配置模式
            config_schema={
                'lookback_period': 'int',
                'price_threshold': 'float',
                'fib_levels': 'list',
                'min_swing_size': 'float'
            },
            default_config={
                'lookback_period': 252,
                'price_threshold': 0.005,
                'fib_levels': [0.236, 0.382, 0.5, 0.618, 0.786],
                'min_swing_size': 0.05
            }
        )
    
    @classmethod
    def create_factor(cls, config: Dict[str, Any] = None) -> BaseFactor:
        config = config or cls.get_default_config()
        return FibonacciFactor(
            lookback_period=config.get('lookback_period', 252),
            price_threshold=config.get('price_threshold', 0.005),
            fib_levels=config.get('fib_levels', [0.236, 0.382, 0.5, 0.618, 0.786]),
            min_swing_size=config.get('min_swing_size', 0.05)
        )


class FibonacciFactor(BaseFactor, VectorizedFactorMixin):
    """斐波那契因子实现"""
    
    def __init__(self, lookback_period: int = 252, price_threshold: float = 0.005,
                 fib_levels: List[float] = None, min_swing_size: float = 0.05):
        super().__init__("fibonacci_factor", "technical")
        self.lookback_period = lookback_period
        self.price_threshold = price_threshold
        self.fib_levels = fib_levels or [0.236, 0.382, 0.5, 0.618, 0.786]
        self.min_swing_size = min_swing_size
        
        self.min_data_length = max(50, lookback_period)
        self.required_columns = ['close', 'high', 'low']
    
    def calculate(self, data: pd.DataFrame, **kwargs) -> Optional[FactorResult]:
        """计算斐波那契因子"""
        try:
            current_price = float(data['close'].iloc[-1])
            
            # 计算斐波那契位
            fib_levels = self._calculate_fibonacci_levels(data)
            
            if not fib_levels:
                return None
            
            # 检测价格是否接近任何斐波那契位
            near_levels = []
            
            for fib_name, fib_value in fib_levels.items():
                if fib_value > 0:
                    deviation = abs(current_price - fib_value) / fib_value
                    if deviation <= self.price_threshold:
                        # 计算重要性权重
                        level_importance = self._get_level_importance(fib_name)
                        
                        near_levels.append({
                            'level': fib_name,
                            'value': fib_value,
                            'deviation': deviation,
                            'importance': level_importance,
                            'proximity_score': (1 - deviation / self.price_threshold) * level_importance,
                            'direction': 'above' if current_price > fib_value else 'below'
                        })
            
            if not near_levels:
                return FactorResult(
                    factor_name=self.factor_name,
                    factor_value=0.0,
                    factor_type=self.factor_type,
                    confidence=0.0,
                    signal_strength="NONE",
                    metadata={
                        'fibonacci_levels': fib_levels,
                        'current_price': current_price,
                        'near_levels_count': 0
                    }
                )
            
            # 计算加权因子值
            total_score = sum(level['proximity_score'] for level in near_levels)
            factor_value = min(1.0, total_score / len(self.fib_levels))
            
            # 计算置信度
            confidence = min(1.0, factor_value * (1 + len(near_levels) * 0.1))
            
            # 确定信号强度
            if factor_value > 0.8 and len(near_levels) >= 2:
                signal_strength = "STRONG"
            elif factor_value > 0.6:
                signal_strength = "MEDIUM"
            elif factor_value > 0.3:
                signal_strength = "WEAK"
            else:
                signal_strength = "NONE"
            
            return FactorResult(
                factor_name=self.factor_name,
                factor_value=factor_value,
                factor_type=self.factor_type,
                confidence=confidence,
                signal_strength=signal_strength,
                metadata={
                    'fibonacci_levels': fib_levels,
                    'near_levels': near_levels,
                    'total_score': total_score,
                    'current_price': current_price
                }
            )
            
        except Exception as e:
            self.logger.error(f"斐波那契因子计算失败: {e}")
            return None
    
    def _calculate_fibonacci_levels(self, data: pd.DataFrame) -> Dict[str, float]:
        """计算斐波那契位 - 使用indicators中的高级方法"""
        try:
            # 使用indicators中的斐波那契计算方法
            # 需要先找到起始低点
            lookback_data = data.tail(self.lookback_period)
            start_low_price = lookback_data['low'].min()

            fib_result = calculate_fibonacci_levels_enhanced(
                df=data,
                start_low_price=start_low_price,
                lookback_period=self.lookback_period
            )

            if not fib_result or 'levels' not in fib_result:
                return {}

            # 转换格式以匹配原有接口
            fib_levels = {}
            levels = fib_result['levels']

            # 提取回撤位
            if 'retracement' in levels:
                for level, value in levels['retracement'].items():
                    if isinstance(level, (int, float)):
                        fib_levels[f'fib_{level:.3f}'] = value
                    else:
                        fib_levels[level] = value

            # 提取关键价位
            if 'key_levels' in fib_result:
                key_levels = fib_result['key_levels']
                if 'swing_high' in key_levels:
                    fib_levels['swing_high'] = key_levels['swing_high']
                if 'swing_low' in key_levels:
                    fib_levels['swing_low'] = key_levels['swing_low']

            # 如果高级方法失败，降级到简单计算
            if not fib_levels:
                return self._calculate_simple_fibonacci_levels(data)

            return fib_levels

        except Exception as e:
            self.logger.warning(f"高级斐波那契计算失败，使用简化版本: {e}")
            return self._calculate_simple_fibonacci_levels(data)

    def _calculate_simple_fibonacci_levels(self, data: pd.DataFrame) -> Dict[str, float]:
        """简化版斐波那契计算（降级方案）"""
        try:
            # 获取回看期内的数据
            lookback_data = data.tail(self.lookback_period)

            # 找到最高点和最低点
            high_idx = lookback_data['high'].idxmax()
            low_idx = lookback_data['low'].idxmin()

            high_price = lookback_data.loc[high_idx, 'high']
            low_price = lookback_data.loc[low_idx, 'low']

            # 检查波动幅度是否足够
            swing_size = (high_price - low_price) / low_price
            if swing_size < self.min_swing_size:
                return {}

            # 计算斐波那契位
            fib_levels = {}
            price_range = high_price - low_price

            # 根据时间顺序确定是回撤还是扩展
            if high_idx > low_idx:  # 上升趋势的回撤
                for level in self.fib_levels:
                    fib_levels[f'fib_{level:.3f}'] = high_price - price_range * level
            else:  # 下降趋势的回撤
                for level in self.fib_levels:
                    fib_levels[f'fib_{level:.3f}'] = low_price + price_range * level

            # 添加关键位
            fib_levels['swing_high'] = high_price
            fib_levels['swing_low'] = low_price

            return fib_levels

        except Exception as e:
            self.logger.error(f"简化斐波那契计算失败: {e}")
            return {}
    
    def _get_level_importance(self, level_name: str) -> float:
        """获取斐波那契位的重要性权重"""
        importance_map = {
            'fib_0.236': 0.6,
            'fib_0.382': 0.8,
            'fib_0.500': 1.0,  # 50%回撤最重要
            'fib_0.618': 0.9,  # 黄金分割位
            'fib_0.786': 0.7,
            'swing_high': 1.0,
            'swing_low': 1.0
        }
        return importance_map.get(level_name, 0.5)


class BollingerBandsFactorPlugin(AdvancedFactorPlugin, VectorizedFactorMixin):
    """高阶布林带因子插件 - 基于波动率建模和趋势过滤"""

    @classmethod
    def get_plugin_info(cls) -> FactorPluginInfo:
        return FactorPluginInfo(
            name="bollinger_bands_factor",
            version="3.0.0",
            description="高阶布林带因子，支持波动率压缩扩张检测、突破真假判断、贴轨效应分析等",
            author="QuantFM Team",
            category="technical",
            dependencies=["close", "volume", "high", "low"],
            scenarios=["intraday", "swing", "after_market"],
            computational_cost="medium",
            priority="high",
            tags=["volatility", "bollinger", "support_resistance", "advanced"],

            # 高级特性
            qlib_compatible=True,
            trainable=True,
            optimizable=True,
            vectorized=True,

            # 性能指标
            min_data_length=300,
            max_lookback=252,
            memory_usage="medium",

            # 配置模式
            config_schema={
                'period': 'int',
                'std_dev': 'float',
                'bbw_lookback': 'int',
                'squeeze_threshold': 'float',
                'expansion_threshold': 'float',
                'breakout_volume_ratio': 'float',
                'walking_days': 'int',
                'walking_tolerance': 'float',
                'trend_confirmation': 'bool',
                'output_mode': 'str'
            },
            default_config={
                'period': 20,
                'std_dev': 2.0,
                'bbw_lookback': 252,
                'squeeze_threshold': 0.2,
                'expansion_threshold': 0.8,
                'breakout_volume_ratio': 1.2,
                'walking_days': 3,
                'walking_tolerance': 0.02,
                'trend_confirmation': True,
                'output_mode': 'boolean'
            }
        )
    
    @classmethod
    def create_factor(cls, config: Dict[str, Any] = None) -> BaseFactor:
        config = config or cls.get_default_config()
        return BollingerBandsFactor(
            period=config.get('period', 20),
            std_dev=config.get('std_dev', 2.0),
            bbw_lookback=config.get('bbw_lookback', 252),
            squeeze_threshold=config.get('squeeze_threshold', 0.2),
            expansion_threshold=config.get('expansion_threshold', 0.8),
            breakout_volume_ratio=config.get('breakout_volume_ratio', 1.2),
            walking_days=config.get('walking_days', 3),
            walking_tolerance=config.get('walking_tolerance', 0.02),
            trend_confirmation=config.get('trend_confirmation', True),
            output_mode=config.get('output_mode', 'boolean')
        )


class BollingerBandsFactor(BaseFactor, VectorizedFactorMixin):
    """高阶布林带因子实现 - 完整移植自bollinger_factor.py"""

    def __init__(self, period: int = 20, std_dev: float = 2.0, bbw_lookback: int = 252,
                 squeeze_threshold: float = 0.2, expansion_threshold: float = 0.8,
                 breakout_volume_ratio: float = 1.2, walking_days: int = 3,
                 walking_tolerance: float = 0.02, trend_confirmation: bool = True,
                 output_mode: str = 'boolean'):
        super().__init__("bollinger_bands_factor", "technical")

        # 核心参数
        self.period = period
        self.std_dev = std_dev
        self.bbw_lookback = bbw_lookback
        self.squeeze_threshold = squeeze_threshold
        self.expansion_threshold = expansion_threshold
        self.breakout_volume_ratio = breakout_volume_ratio
        self.walking_days = walking_days
        self.walking_tolerance = walking_tolerance
        self.trend_confirmation = trend_confirmation
        self.output_mode = output_mode

        # 趋势确认参数
        self.middle_slope_period = 5

        # 设置数据要求
        self.min_data_length = max(period + bbw_lookback, 300)
        self.required_columns = ['close', 'volume', 'high', 'low']
    
    def calculate(self, data: pd.DataFrame, **kwargs) -> Optional[FactorResult]:
        """计算高阶布林带因子"""
        try:
            # 数据验证
            if len(data) < self.min_data_length:
                return None

            # 创建工作副本
            df = data.copy()

            # 计算基础布林带指标
            self._calculate_basic_bollinger(df)

            # 计算成交量比率
            self._calculate_volume_ratio(df)

            # 计算BBW历史分位数
            self._calculate_bbw_percentile(df)

            # 计算中轨斜率（趋势确认）
            if self.trend_confirmation:
                self._calculate_middle_band_slope(df)

            # 初始化信号列
            self._initialize_signal_columns(df)

            # 检测各种信号
            self._detect_squeeze_expansion_signals(df)
            self._detect_breakout_signals(df)
            self._detect_walking_signals(df)

            if self.trend_confirmation:
                self._detect_middle_band_signals(df)

            # 计算最终结果
            return self._calculate_final_result(df)

        except Exception as e:
            self.logger.error(f"布林带因子计算失败: {e}")
            return None

    def _calculate_basic_bollinger(self, df: pd.DataFrame):
        """计算基础布林带指标（向量化实现）"""
        # 计算移动平均线（中轨）
        df['bb_middle'] = df['close'].rolling(window=self.period).mean()

        # 计算标准差
        df['bb_std'] = df['close'].rolling(window=self.period).std()

        # 计算上下轨
        df['bb_upper'] = df['bb_middle'] + (self.std_dev * df['bb_std'])
        df['bb_lower'] = df['bb_middle'] - (self.std_dev * df['bb_std'])

        # 计算%B指标（价格在布林带中的位置）
        df['bb_percent'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])

        # 计算布林带宽度（BBW）
        df['bbw'] = (df['bb_upper'] - df['bb_lower']) / df['bb_middle']

    def _calculate_volume_ratio(self, df: pd.DataFrame):
        """计算成交量比率（向量化实现）"""
        # 计算20日平均成交量
        df['volume_ma'] = df['volume'].rolling(window=20).mean()

        # 计算成交量比率
        df['volume_ratio'] = df['volume'] / df['volume_ma']
        df['volume_ratio'] = df['volume_ratio'].fillna(1.0)

    def _calculate_bbw_percentile(self, df: pd.DataFrame):
        """计算BBW历史分位数（向量化实现）"""
        # 计算BBW的历史分位数
        df['bbw_percentile'] = df['bbw'].rolling(window=self.bbw_lookback).rank(pct=True)
        df['bbw_percentile'] = df['bbw_percentile'].fillna(0.5)

    def _calculate_middle_band_slope(self, df: pd.DataFrame):
        """计算中轨斜率（趋势确认）"""
        # 计算中轨的斜率
        df['bb_middle_slope'] = df['bb_middle'].diff(self.middle_slope_period) / self.middle_slope_period
        df['bb_middle_slope'] = df['bb_middle_slope'].fillna(0.0)

    def _initialize_signal_columns(self, df: pd.DataFrame):
        """初始化信号列"""
        signal_columns = [
            'bb_squeeze_signal', 'bb_expansion_signal',
            'bb_breakout_up', 'bb_breakout_down',
            'bb_walking_up', 'bb_walking_down',
            'bb_middle_support', 'bb_middle_resistance'
        ]

        for col in signal_columns:
            df[col] = False

    def _detect_squeeze_expansion_signals(self, df: pd.DataFrame):
        """检测压缩和扩张信号（向量化实现）"""
        # 压缩信号：BBW分位数低于阈值
        df['bb_squeeze_signal'] = df['bbw_percentile'] <= self.squeeze_threshold

        # 扩张信号：BBW分位数高于阈值
        df['bb_expansion_signal'] = df['bbw_percentile'] >= self.expansion_threshold

    def _detect_breakout_signals(self, df: pd.DataFrame):
        """检测突破信号（向量化实现）"""
        # 向上突破：收盘价突破上轨且有成交量配合
        volume_condition = df['volume_ratio'] >= self.breakout_volume_ratio

        df['bb_breakout_up'] = (
            (df['close'] > df['bb_upper']) &
            (df['close'].shift(1) <= df['bb_upper'].shift(1)) &
            volume_condition
        )

        # 向下突破：收盘价跌破下轨且有成交量配合
        df['bb_breakout_down'] = (
            (df['close'] < df['bb_lower']) &
            (df['close'].shift(1) >= df['bb_lower'].shift(1)) &
            volume_condition
        )

    def _detect_walking_signals(self, df: pd.DataFrame):
        """检测贴轨效应信号（向量化实现）"""
        # 计算价格与上轨的距离比例
        upper_distance = abs(df['close'] - df['bb_upper']) / df['bb_upper']
        lower_distance = abs(df['close'] - df['bb_lower']) / df['bb_lower']

        # 贴上轨：连续几天接近上轨
        near_upper = upper_distance <= self.walking_tolerance
        df['bb_walking_up'] = near_upper.rolling(window=self.walking_days).sum() >= self.walking_days

        # 贴下轨：连续几天接近下轨
        near_lower = lower_distance <= self.walking_tolerance
        df['bb_walking_down'] = near_lower.rolling(window=self.walking_days).sum() >= self.walking_days

    def _detect_middle_band_signals(self, df: pd.DataFrame):
        """检测中轨支撑阻力信号（向量化实现）"""
        # 中轨支撑：价格从下方接近中轨且中轨向上
        df['bb_middle_support'] = (
            (df['close'] > df['bb_middle']) &
            (df['close'].shift(1) <= df['bb_middle'].shift(1)) &
            (df['bb_middle_slope'] > 0)
        )

        # 中轨阻力：价格从上方接近中轨且中轨向下
        df['bb_middle_resistance'] = (
            (df['close'] < df['bb_middle']) &
            (df['close'].shift(1) >= df['bb_middle'].shift(1)) &
            (df['bb_middle_slope'] < 0)
        )

    def _calculate_final_result(self, df: pd.DataFrame) -> Optional[FactorResult]:
        """计算最终因子结果"""
        try:
            # 获取最后一行数据
            last_row = df.iloc[-1]

            # 计算综合因子值
            factor_value = 0.0
            signals = []

            # 压缩扩张信号（权重较高）
            if last_row['bb_squeeze_signal']:
                factor_value += 0.4
                signals.append('squeeze')

            if last_row['bb_expansion_signal']:
                factor_value += 0.6
                signals.append('expansion')

            # 突破信号（权重最高）
            if last_row['bb_breakout_up']:
                factor_value += 0.8
                signals.append('breakout_up')

            if last_row['bb_breakout_down']:
                factor_value += 0.6
                signals.append('breakout_down')

            # 贴轨信号（权重中等）
            if last_row['bb_walking_up']:
                factor_value += 0.7
                signals.append('walking_up')

            if last_row['bb_walking_down']:
                factor_value += 0.5
                signals.append('walking_down')

            # 中轨信号（权重较低）
            if self.trend_confirmation:
                if last_row['bb_middle_support']:
                    factor_value += 0.3
                    signals.append('middle_support')

                if last_row['bb_middle_resistance']:
                    factor_value += 0.2
                    signals.append('middle_resistance')

            # 归一化因子值
            factor_value = min(1.0, factor_value)

            # 计算置信度
            bb_percent = last_row['bb_percent']
            bbw_percentile = last_row['bbw_percentile']

            # 基于%B位置和BBW分位数计算置信度
            position_confidence = abs(bb_percent - 0.5) * 2  # %B越极端置信度越高
            volatility_confidence = min(bbw_percentile, 1 - bbw_percentile) * 2  # BBW越极端置信度越高

            confidence = min(1.0, (position_confidence + volatility_confidence) / 2 + 0.2)

            # 确定信号强度
            if factor_value > 0.8 and len(signals) >= 2:
                signal_strength = "STRONG"
            elif factor_value > 0.6:
                signal_strength = "MEDIUM"
            elif factor_value > 0.3:
                signal_strength = "WEAK"
            else:
                signal_strength = "NONE"

            # 构建元数据
            metadata = {
                'signals': signals,
                'bb_percent': float(bb_percent),
                'bbw': float(last_row['bbw']),
                'bbw_percentile': float(bbw_percentile),
                'upper_band': float(last_row['bb_upper']),
                'middle_band': float(last_row['bb_middle']),
                'lower_band': float(last_row['bb_lower']),
                'volume_ratio': float(last_row['volume_ratio']),
                'current_price': float(last_row['close'])
            }

            if self.trend_confirmation:
                metadata['middle_slope'] = float(last_row['bb_middle_slope'])

            return FactorResult(
                factor_name=self.factor_name,
                factor_value=factor_value,
                factor_type=self.factor_type,
                confidence=confidence,
                signal_strength=signal_strength,
                metadata=metadata
            )

        except Exception as e:
            self.logger.error(f"计算最终结果失败: {e}")
            return None

    # 高阶布林带因子实现完成，移除了简化版本方法
