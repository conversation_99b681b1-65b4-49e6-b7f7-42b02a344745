#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
复合因子插件

组合多个基础因子的复合因子插件
"""

import numpy as np
import pandas as pd
from typing import Dict, Any, Optional, List, Tuple

from ..core.base_plugin import AdvancedFactorPlugin, FactorPluginInfo
from ..base_factor import BaseFactor, FactorResult, CompositeFactor
from ..technical.trend_plugins import EmaFactor, MacdFactor
from ..technical.support_resistance_plugins import FibonacciFactor, BollingerBandsFactor
from ..volume.volume_plugins import VolumeSpikeFactor, PriceVolumeFactor


class TechnicalCompositeFactorPlugin(AdvancedFactorPlugin):
    """技术分析复合因子插件"""
    
    @classmethod
    def get_plugin_info(cls) -> FactorPluginInfo:
        return FactorPluginInfo(
            name="technical_composite_factor",
            version="2.0.0",
            description="技术分析复合因子，综合趋势、支撑阻力和成交量信号",
            author="QuantFM Team",
            category="composite",
            dependencies=["close", "high", "low", "volume"],
            scenarios=["intraday", "swing", "after_market"],
            computational_cost="high",
            priority="high",
            tags=["composite", "technical", "multi_factor"],
            
            # 高级特性
            qlib_compatible=True,
            trainable=True,
            optimizable=True,
            vectorized=False,  # 复合因子通常不是向量化的
            
            # 性能指标
            min_data_length=100,
            max_lookback=252,
            memory_usage="high",
            
            # 配置模式
            config_schema={
                'trend_weight': 'float',
                'support_resistance_weight': 'float',
                'volume_weight': 'float',
                'enable_ema': 'bool',
                'enable_macd': 'bool',
                'enable_fibonacci': 'bool',
                'enable_bollinger': 'bool',
                'enable_volume_spike': 'bool',
                'enable_price_volume': 'bool'
            },
            default_config={
                'trend_weight': 0.4,
                'support_resistance_weight': 0.4,
                'volume_weight': 0.2,
                'enable_ema': True,
                'enable_macd': True,
                'enable_fibonacci': True,
                'enable_bollinger': True,
                'enable_volume_spike': True,
                'enable_price_volume': False  # 默认关闭，计算成本较高
            }
        )
    
    @classmethod
    def create_factor(cls, config: Dict[str, Any] = None) -> BaseFactor:
        config = config or cls.get_default_config()
        return TechnicalCompositeFactor(
            trend_weight=config.get('trend_weight', 0.4),
            support_resistance_weight=config.get('support_resistance_weight', 0.4),
            volume_weight=config.get('volume_weight', 0.2),
            factor_config=config
        )


class TechnicalCompositeFactor(BaseFactor):
    """技术分析复合因子实现"""
    
    def __init__(self, trend_weight: float = 0.4, 
                 support_resistance_weight: float = 0.4,
                 volume_weight: float = 0.2,
                 factor_config: Dict[str, Any] = None):
        super().__init__("technical_composite_factor", "composite")
        
        self.trend_weight = trend_weight
        self.support_resistance_weight = support_resistance_weight
        self.volume_weight = volume_weight
        self.factor_config = factor_config or {}
        
        # 初始化子因子
        self.sub_factors = self._init_sub_factors()
        
        self.min_data_length = 252
        self.required_columns = ['close', 'high', 'low', 'volume']
    
    def _init_sub_factors(self) -> Dict[str, BaseFactor]:
        """初始化子因子"""
        factors = {}
        
        # 趋势类因子
        if self.factor_config.get('enable_ema', True):
            factors['ema'] = EmaFactor()
        
        if self.factor_config.get('enable_macd', True):
            factors['macd'] = MacdFactor()
        
        # 支撑阻力类因子
        if self.factor_config.get('enable_fibonacci', True):
            factors['fibonacci'] = FibonacciFactor()
        
        if self.factor_config.get('enable_bollinger', True):
            factors['bollinger'] = BollingerBandsFactor()
        
        # 成交量类因子
        if self.factor_config.get('enable_volume_spike', True):
            factors['volume_spike'] = VolumeSpikeFactor()
        
        if self.factor_config.get('enable_price_volume', False):
            factors['price_volume'] = PriceVolumeFactor()
        
        return factors
    
    def calculate(self, data: pd.DataFrame, **kwargs) -> Optional[FactorResult]:
        """计算复合因子"""
        try:
            # 计算所有子因子
            sub_results = {}
            
            for factor_name, factor in self.sub_factors.items():
                try:
                    result = factor.calculate_with_validation(data, **kwargs)
                    if result:
                        sub_results[factor_name] = result
                except Exception as e:
                    self.logger.warning(f"子因子 {factor_name} 计算失败: {e}")
            
            if not sub_results:
                return None
            
            # 按类别分组计算
            trend_factors = ['ema', 'macd']
            support_resistance_factors = ['fibonacci', 'bollinger']
            volume_factors = ['volume_spike', 'price_volume']
            
            trend_score = self._calculate_category_score(sub_results, trend_factors)
            sr_score = self._calculate_category_score(sub_results, support_resistance_factors)
            volume_score = self._calculate_category_score(sub_results, volume_factors)
            
            # 加权计算最终因子值
            factor_value = (
                trend_score * self.trend_weight +
                sr_score * self.support_resistance_weight +
                volume_score * self.volume_weight
            )
            
            # 计算置信度（基于有效子因子数量和一致性）
            valid_factors = len(sub_results)
            total_factors = len(self.sub_factors)
            coverage = valid_factors / total_factors
            
            # 计算信号一致性
            consistency = self._calculate_signal_consistency(sub_results)
            
            confidence = min(1.0, coverage * consistency * factor_value + 0.2)
            
            # 确定信号强度
            if factor_value > 0.8 and consistency > 0.7:
                signal_strength = "STRONG"
            elif factor_value > 0.6 and consistency > 0.5:
                signal_strength = "MEDIUM"
            elif factor_value > 0.3:
                signal_strength = "WEAK"
            else:
                signal_strength = "NONE"
            
            return FactorResult(
                factor_name=self.factor_name,
                factor_value=factor_value,
                factor_type=self.factor_type,
                confidence=confidence,
                signal_strength=signal_strength,
                metadata={
                    'sub_results': {k: v.factor_value for k, v in sub_results.items()},
                    'trend_score': trend_score,
                    'support_resistance_score': sr_score,
                    'volume_score': volume_score,
                    'coverage': coverage,
                    'consistency': consistency,
                    'valid_factors': valid_factors,
                    'total_factors': total_factors
                }
            )
            
        except Exception as e:
            self.logger.error(f"技术分析复合因子计算失败: {e}")
            return None
    
    def _calculate_category_score(self, sub_results: Dict[str, FactorResult], 
                                factor_names: List[str]) -> float:
        """计算类别得分"""
        valid_results = []
        
        for factor_name in factor_names:
            if factor_name in sub_results:
                result = sub_results[factor_name]
                # 加权：因子值 * 置信度
                weighted_value = result.factor_value * result.confidence
                valid_results.append(weighted_value)
        
        if not valid_results:
            return 0.0
        
        return np.mean(valid_results)
    
    def _calculate_signal_consistency(self, sub_results: Dict[str, FactorResult]) -> float:
        """计算信号一致性"""
        try:
            if len(sub_results) < 2:
                return 1.0
            
            # 获取所有因子值
            factor_values = [result.factor_value for result in sub_results.values()]
            
            # 计算标准差（一致性的反向指标）
            std_dev = np.std(factor_values)
            
            # 转换为一致性分数（0-1）
            consistency = max(0.0, 1.0 - std_dev)
            
            return consistency
            
        except Exception:
            return 0.5


class MomentumCompositeFactorPlugin(AdvancedFactorPlugin):
    """动量复合因子插件"""
    
    @classmethod
    def get_plugin_info(cls) -> FactorPluginInfo:
        return FactorPluginInfo(
            name="momentum_composite_factor",
            version="2.0.0",
            description="动量复合因子，专注于价格动量和趋势强度",
            author="QuantFM Team",
            category="composite",
            dependencies=["close", "volume"],
            scenarios=["swing", "position"],
            computational_cost="medium",
            priority="medium",
            tags=["composite", "momentum", "trend_strength"],
            
            # 高级特性
            qlib_compatible=True,
            trainable=True,
            optimizable=True,
            vectorized=False,
            
            # 性能指标
            min_data_length=100,
            max_lookback=200,
            memory_usage="medium",
            
            # 配置模式
            config_schema={
                'macd_weight': 'float',
                'volume_weight': 'float',
                'lookback_period': 'int'
            },
            default_config={
                'macd_weight': 0.7,
                'volume_weight': 0.3,
                'lookback_period': 20
            }
        )
    
    @classmethod
    def create_factor(cls, config: Dict[str, Any] = None) -> BaseFactor:
        config = config or cls.get_default_config()
        return MomentumCompositeFactor(
            macd_weight=config.get('macd_weight', 0.7),
            volume_weight=config.get('volume_weight', 0.3),
            lookback_period=config.get('lookback_period', 20)
        )


class MomentumCompositeFactor(BaseFactor):
    """动量复合因子实现"""
    
    def __init__(self, macd_weight: float = 0.7, volume_weight: float = 0.3,
                 lookback_period: int = 20):
        super().__init__("momentum_composite_factor", "composite")
        
        self.macd_weight = macd_weight
        self.volume_weight = volume_weight
        self.lookback_period = lookback_period
        
        # 初始化子因子
        self.macd_factor = MacdFactor()
        self.volume_factor = VolumeSpikeFactor(lookback_period=lookback_period)
        
        self.min_data_length = 100
        self.required_columns = ['close', 'volume']
    
    def calculate(self, data: pd.DataFrame, **kwargs) -> Optional[FactorResult]:
        """计算动量复合因子"""
        try:
            # 计算MACD因子
            macd_result = self.macd_factor.calculate_with_validation(data, **kwargs)
            
            # 计算成交量因子
            volume_result = self.volume_factor.calculate_with_validation(data, **kwargs)
            
            if not macd_result and not volume_result:
                return None
            
            # 计算加权因子值
            factor_value = 0.0
            total_weight = 0.0
            
            if macd_result:
                factor_value += macd_result.factor_value * macd_result.confidence * self.macd_weight
                total_weight += self.macd_weight
            
            if volume_result:
                factor_value += volume_result.factor_value * volume_result.confidence * self.volume_weight
                total_weight += self.volume_weight
            
            if total_weight > 0:
                factor_value /= total_weight
            
            # 计算置信度
            confidence = 0.0
            if macd_result:
                confidence += macd_result.confidence * self.macd_weight
            if volume_result:
                confidence += volume_result.confidence * self.volume_weight
            
            if total_weight > 0:
                confidence /= total_weight
            
            # 确定信号强度
            if factor_value > 0.8:
                signal_strength = "STRONG"
            elif factor_value > 0.6:
                signal_strength = "MEDIUM"
            elif factor_value > 0.3:
                signal_strength = "WEAK"
            else:
                signal_strength = "NONE"
            
            return FactorResult(
                factor_name=self.factor_name,
                factor_value=factor_value,
                factor_type=self.factor_type,
                confidence=confidence,
                signal_strength=signal_strength,
                metadata={
                    'macd_value': macd_result.factor_value if macd_result else 0.0,
                    'volume_value': volume_result.factor_value if volume_result else 0.0,
                    'macd_confidence': macd_result.confidence if macd_result else 0.0,
                    'volume_confidence': volume_result.confidence if volume_result else 0.0,
                    'total_weight': total_weight
                }
            )
            
        except Exception as e:
            self.logger.error(f"动量复合因子计算失败: {e}")
            return None
