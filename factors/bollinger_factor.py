#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高阶布林带因子

基于波动率建模和趋势过滤的高阶布林带应用。
支持多种输出模式和使用场景，通过配置文件统一管理参数。

核心特性：
1. 波动率压缩与扩张检测
2. 布林带突破真假判断
3. 贴轨效应量化分析
4. 中轨二次确认机制
5. 多维度信号验证
6. Qlib兼容性（可选）
7. 参数优化支持
8. 高性能向量化计算

高阶应用理念：
- 从传统支撑阻力 → 波动率建模 + 趋势过滤
- BBW(布林带宽度)历史分位数分析
- %B指标结合MACD背离判断
- 多时间框架共振验证

作者: QuantFM Team
创建时间: 2025-08-23
"""

import pandas as pd
import numpy as np
import talib
import yaml
import os
from typing import Dict, List, Tuple, Optional, Union, Any
import logging
from dataclasses import dataclass
from .base_factor import BaseFactor, FactorResult


@dataclass
class BollingerConfig:
    """高阶布林带因子配置类"""
    # 布林带基础参数
    period: int = 20                    # 布林带周期
    std_dev: float = 2.0               # 标准差倍数

    # 波动率分析参数
    bbw_lookback: int = 252            # BBW历史分位数计算周期
    squeeze_threshold: float = 0.2     # 波动率压缩阈值（历史分位数）
    expansion_threshold: float = 0.8   # 波动率扩张阈值（历史分位数）

    # 突破验证参数
    breakout_volume_ratio: float = 1.2 # 突破时成交量放大倍数
    fake_breakout_days: int = 3        # 假突破判断天数

    # 贴轨效应参数
    walking_days: int = 3              # 贴轨最少天数
    walking_tolerance: float = 0.02    # 贴轨容忍度

    # 中轨确认参数
    middle_slope_period: int = 5       # 中轨斜率计算周期
    trend_confirmation: bool = True    # 是否启用趋势确认

    # 输出控制
    output_mode: str = "continuous"    # "boolean" | "continuous"
    normalize_output: bool = True      # 是否标准化输出
    feature_engineering: bool = True   # 是否启用特征工程

    # 性能优化
    smoothing_window: int = 3          # 信号平滑窗口
    strength_decay: float = 0.95       # 信号强度衰减系数
    enable_cache: bool = True          # 是否启用缓存
    vectorized_compute: bool = True    # 是否启用向量化计算

    # Qlib集成
    enable_qlib_mode: bool = False     # 是否启用Qlib模式
    auto_optimization: bool = False    # 是否自动优化参数
    ml_ready: bool = True              # 是否为机器学习准备

    @classmethod
    def from_config_file(cls, config_path: str = None, scenario: str = "production") -> 'BollingerConfig':
        """从配置文件加载配置"""
        if config_path is None:
            # 默认配置文件路径
            current_dir = os.path.dirname(os.path.abspath(__file__))
            config_path = os.path.join(os.path.dirname(current_dir), "config", "factor_params.yaml")

        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)

            # 获取布林带因子配置
            bb_config = config_data.get('bollinger_bands', {})

            # 如果指定了场景，使用场景配置覆盖
            if scenario and scenario in config_data.get('scenarios', {}):
                scenario_config = config_data['scenarios'][scenario].get('bollinger_bands', {})
                bb_config = cls._merge_configs(bb_config, scenario_config)

            # 展平嵌套配置
            flat_config = {}
            for section, params in bb_config.items():
                if section == 'param_space':
                    # 跳过参数空间定义，不作为实际参数
                    continue
                elif isinstance(params, dict):
                    flat_config.update(params)
                else:
                    flat_config[section] = params

            return cls(**flat_config)

        except Exception as e:
            logging.warning(f"配置文件加载失败，使用默认配置: {e}")
            return cls()

    @staticmethod
    def _merge_configs(base_config: dict, override_config: dict) -> dict:
        """合并配置"""
        merged = base_config.copy()
        for key, value in override_config.items():
            if isinstance(value, dict) and key in merged:
                merged[key].update(value)
            else:
                merged[key] = value
        return merged

    def get_param_space(self) -> Dict[str, Dict]:
        """获取参数空间（用于优化）"""
        return {
            'period': {'type': 'int', 'range': [10, 30], 'default': self.period},
            'std_dev': {'type': 'float', 'range': [1.5, 2.5], 'default': self.std_dev},
            'bbw_lookback': {'type': 'int', 'range': [126, 504], 'default': self.bbw_lookback},
            'squeeze_threshold': {'type': 'float', 'range': [0.1, 0.3], 'default': self.squeeze_threshold},
            'expansion_threshold': {'type': 'float', 'range': [0.7, 0.9], 'default': self.expansion_threshold},
            'breakout_volume_ratio': {'type': 'float', 'range': [1.1, 2.0], 'default': self.breakout_volume_ratio},
            'walking_days': {'type': 'int', 'range': [2, 5], 'default': self.walking_days},
            'smoothing_window': {'type': 'int', 'range': [1, 5], 'default': self.smoothing_window},
            'strength_decay': {'type': 'float', 'range': [0.8, 0.99], 'default': self.strength_decay}
        }


class BollingerFactor(BaseFactor):
    """
    高阶布林带因子

    支持多种输出模式和使用场景的布林带因子实现。
    完全兼容原有功能，包括Qlib支持、参数优化等。
    """

    def __init__(self, config: BollingerConfig = None, scenario: str = "production", **kwargs):
        """
        初始化高阶布林带因子

        Args:
            config: 因子配置对象
            scenario: 使用场景 ("development", "production", "qlib_training")
            **kwargs: 直接参数覆盖
        """
        super().__init__("bollinger_factor", "technical")

        # 配置初始化
        if config is None:
            config = BollingerConfig.from_config_file(scenario=scenario)

        # 参数覆盖
        for key, value in kwargs.items():
            if hasattr(config, key):
                setattr(config, key, value)

        self.config = config
        self.scenario = scenario

        # 因子缓存
        self._factor_cache = {} if self.config.enable_cache else None

        # 设置数据要求
        self.required_columns = ['close', 'volume', 'high', 'low']
        self.min_data_length = max(self.config.period + self.config.bbw_lookback, 300)

        self.logger = logging.getLogger(self.__class__.__name__)

        # 根据配置决定计算方法
        if self.config.output_mode == "boolean":
            self._calculate_method = self._calculate_boolean_factors
        else:
            self._calculate_method = self._calculate_continuous_factors

    @classmethod
    def create_from_params(cls, params: Dict[str, Any], scenario: str = "production") -> 'BollingerFactor':
        """从参数字典创建因子实例（用于参数优化）"""
        config = BollingerConfig.from_config_file(scenario=scenario)

        # 更新参数
        for key, value in params.items():
            if hasattr(config, key):
                setattr(config, key, value)

        return cls(config)

    @classmethod
    def get_param_space(cls) -> Dict[str, Dict]:
        """获取参数空间定义（用于优化）"""
        config = BollingerConfig()
        return config.get_param_space()

    def calculate(self, data: pd.DataFrame, **kwargs) -> Optional[FactorResult]:
        """
        计算高阶布林带因子

        Args:
            data: 包含OHLCV数据的DataFrame

        Returns:
            添加了布林带因子的DataFrame或FactorResult
        """
        try:
            # 检查输入数据
            if data is None:
                self.logger.error("输入数据为None")
                return None

            if len(data) < self.min_data_length:
                self.logger.warning(f"数据长度不足")
                return self._add_empty_factors(data) if self.config.output_mode == "continuous" else None

            df_result = data.copy()

            # 1. 计算布林带基础指标
            df_result = self._calculate_bollinger_indicators(df_result)

            # 2. 根据配置选择计算方法
            if self.config.output_mode == "boolean":
                return self._calculate_method(df_result)
            else:
                df_result = self._calculate_method(df_result)

                # 3. 后处理
                if self.config.normalize_output:
                    df_result = self._normalize_factors(df_result)

                return df_result

        except Exception as e:
            self.logger.error(f"计算高阶布林带因子失败: {e}")
            return self._add_empty_factors(data) if self.config.output_mode == "continuous" else None

    def _calculate_bollinger_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算布林带基础指标（向量化实现）"""
        # 计算布林带
        df['bb_middle'] = df['close'].rolling(window=self.config.period).mean()
        bb_std = df['close'].rolling(window=self.config.period).std()
        df['bb_upper'] = df['bb_middle'] + (bb_std * self.config.std_dev)
        df['bb_lower'] = df['bb_middle'] - (bb_std * self.config.std_dev)

        # 计算BBW（布林带宽度）
        df['bbw'] = (df['bb_upper'] - df['bb_lower']) / df['bb_middle']

        # 计算%B指标
        df['bb_percent'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])

        # 计算BBW历史分位数
        df['bbw_percentile'] = df['bbw'].rolling(window=self.config.bbw_lookback).rank(pct=True)

        # 计算中轨斜率
        if self.config.trend_confirmation:
            df['bb_middle_slope'] = df['bb_middle'].diff(self.config.middle_slope_period) / self.config.middle_slope_period

        # 计算成交量比率
        df['volume_ratio'] = df['volume'] / df['volume'].rolling(window=20).mean()

        return df

    def _calculate_boolean_factors(self, df: pd.DataFrame) -> Optional[FactorResult]:
        """计算布尔信号因子（策略信号模式）"""
        # 初始化因子列
        df['bb_squeeze_signal'] = False        # 波动率压缩信号
        df['bb_expansion_signal'] = False      # 波动率扩张信号
        df['bb_breakout_up'] = False          # 向上突破信号
        df['bb_breakout_down'] = False        # 向下突破信号
        df['bb_walking_up'] = False           # 上轨贴轨信号
        df['bb_walking_down'] = False         # 下轨贴轨信号
        df['bb_middle_support'] = False       # 中轨支撑信号
        df['bb_middle_resistance'] = False    # 中轨阻力信号

        # 检测波动率压缩信号
        squeeze_condition = (
            (df['bbw_percentile'] < self.config.squeeze_threshold) &
            (df['bbw_percentile'].shift(1) >= self.config.squeeze_threshold)
        )
        df.loc[squeeze_condition, 'bb_squeeze_signal'] = True

        # 检测波动率扩张信号
        expansion_condition = (
            (df['bbw_percentile'] > self.config.expansion_threshold) &
            (df['bbw_percentile'].shift(1) <= self.config.expansion_threshold) &
            (df['volume_ratio'] > self.config.breakout_volume_ratio)
        )
        df.loc[expansion_condition, 'bb_expansion_signal'] = True

        # 检测突破信号
        self._detect_breakout_signals(df)

        # 检测贴轨信号
        self._detect_walking_signals(df)

        # 检测中轨确认信号
        if self.config.trend_confirmation:
            self._detect_middle_band_signals(df)

        # 返回最新的信号状态
        latest_signals = {}
        signal_columns = [col for col in df.columns if col.startswith('bb_') and col.endswith('_signal') or
                         col.startswith('bb_') and ('breakout' in col or 'walking' in col or 'support' in col or 'resistance' in col)]

        for col in signal_columns:
            latest_signals[col] = bool(df[col].iloc[-1])

        # 计算综合信号强度
        active_signals = sum(latest_signals.values())
        if active_signals >= 3:
            signal_strength = "STRONG"
        elif active_signals >= 2:
            signal_strength = "MEDIUM"
        elif active_signals >= 1:
            signal_strength = "WEAK"
        else:
            signal_strength = "NONE"

        return FactorResult(
            factor_name=self.factor_name,
            factor_value=float(active_signals),
            factor_type=self.factor_type,
            confidence=min(active_signals * 0.25, 1.0),
            signal_strength=signal_strength,
            metadata={
                'signals': latest_signals,
                'bb_values': {
                    'upper': float(df['bb_upper'].iloc[-1]),
                    'middle': float(df['bb_middle'].iloc[-1]),
                    'lower': float(df['bb_lower'].iloc[-1]),
                    'bbw': float(df['bbw'].iloc[-1]),
                    'bb_percent': float(df['bb_percent'].iloc[-1]),
                    'bbw_percentile': float(df['bbw_percentile'].iloc[-1])
                }
            }
        )

    def _calculate_continuous_factors(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算连续数值因子（机器学习模式）"""
        # 初始化因子列
        df['bb_squeeze_strength'] = 0.0       # 波动率压缩强度
        df['bb_expansion_strength'] = 0.0     # 波动率扩张强度
        df['bb_breakout_strength'] = 0.0      # 突破强度
        df['bb_walking_strength'] = 0.0       # 贴轨强度
        df['bb_trend_alignment'] = 0.0        # 趋势一致性
        df['bb_volume_confirmation'] = 0.0    # 成交量确认度
        df['bb_position_score'] = 0.0         # 位置评分
        df['bb_momentum_score'] = 0.0         # 动量评分

        # 计算各种强度指标
        self._calculate_squeeze_strength(df)
        self._calculate_expansion_strength(df)
        self._calculate_breakout_strength(df)
        self._calculate_walking_strength(df)
        self._calculate_trend_alignment(df)
        self._calculate_volume_confirmation(df)
        self._calculate_position_score(df)
        self._calculate_momentum_score(df)

        # 特征工程
        if self.config.feature_engineering:
            df = self._feature_engineering(df)

        # 平滑和衰减
        if self.config.smoothing_window > 1 or self.config.strength_decay < 1.0:
            df = self._apply_smoothing_and_decay(df)

        return df

    def _detect_breakout_signals(self, df: pd.DataFrame):
        """检测突破信号（向量化实现）"""
        # 向上突破条件
        up_breakout = (
            (df['close'] > df['bb_upper']) &
            (df['close'].shift(1) <= df['bb_upper'].shift(1)) &
            (df['bbw_percentile'] > self.config.squeeze_threshold) &
            (df['volume_ratio'] > self.config.breakout_volume_ratio)
        )

        # 向下突破条件
        down_breakout = (
            (df['close'] < df['bb_lower']) &
            (df['close'].shift(1) >= df['bb_lower'].shift(1)) &
            (df['bbw_percentile'] > self.config.squeeze_threshold) &
            (df['volume_ratio'] > self.config.breakout_volume_ratio)
        )

        # 趋势确认
        if self.config.trend_confirmation:
            up_breakout = up_breakout & (df['bb_middle_slope'] > 0)
            down_breakout = down_breakout & (df['bb_middle_slope'] < 0)

        df.loc[up_breakout, 'bb_breakout_up'] = True
        df.loc[down_breakout, 'bb_breakout_down'] = True

    def _detect_walking_signals(self, df: pd.DataFrame):
        """检测贴轨信号（向量化实现）"""
        # 计算价格与轨道的距离
        upper_distance = abs(df['close'] - df['bb_upper']) / df['bb_upper']
        lower_distance = abs(df['close'] - df['bb_lower']) / df['bb_lower']

        # 上轨贴轨条件
        upper_walking = upper_distance < self.config.walking_tolerance

        # 下轨贴轨条件
        lower_walking = lower_distance < self.config.walking_tolerance

        # 连续贴轨检测
        for i in range(self.config.walking_days, len(df)):
            if upper_walking.iloc[i-self.config.walking_days:i+1].all():
                df.iloc[i, df.columns.get_loc('bb_walking_up')] = True

            if lower_walking.iloc[i-self.config.walking_days:i+1].all():
                df.iloc[i, df.columns.get_loc('bb_walking_down')] = True

    def _detect_middle_band_signals(self, df: pd.DataFrame):
        """检测中轨确认信号（向量化实现）"""
        # 中轨支撑：价格从上方回踩中轨不破
        support_condition = (
            (df['close'] > df['bb_middle']) &
            (df['low'] <= df['bb_middle'] * 1.01) &  # 轻微触及中轨
            (df['close'] > df['bb_middle']) &  # 收盘仍在中轨上方
            (df['bb_middle_slope'] > 0)  # 中轨向上
        )

        # 中轨阻力：价格从下方反弹到中轨遇阻
        resistance_condition = (
            (df['close'] < df['bb_middle']) &
            (df['high'] >= df['bb_middle'] * 0.99) &  # 轻微触及中轨
            (df['close'] < df['bb_middle']) &  # 收盘仍在中轨下方
            (df['bb_middle_slope'] < 0)  # 中轨向下
        )

        df.loc[support_condition, 'bb_middle_support'] = True
        df.loc[resistance_condition, 'bb_middle_resistance'] = True

    def _calculate_squeeze_strength(self, df: pd.DataFrame):
        """计算波动率压缩强度（向量化实现）"""
        # 基于BBW历史分位数计算压缩强度
        df['bb_squeeze_strength'] = np.where(
            df['bbw_percentile'] < self.config.squeeze_threshold,
            (self.config.squeeze_threshold - df['bbw_percentile']) / self.config.squeeze_threshold,
            0.0
        )

    def _calculate_expansion_strength(self, df: pd.DataFrame):
        """计算波动率扩张强度（向量化实现）"""
        # 基于BBW历史分位数和成交量确认
        expansion_base = np.where(
            df['bbw_percentile'] > self.config.expansion_threshold,
            (df['bbw_percentile'] - self.config.expansion_threshold) / (1 - self.config.expansion_threshold),
            0.0
        )

        # 成交量确认加权
        volume_weight = np.minimum(df['volume_ratio'] / self.config.breakout_volume_ratio, 2.0)
        df['bb_expansion_strength'] = expansion_base * volume_weight

    def _calculate_breakout_strength(self, df: pd.DataFrame):
        """计算突破强度（向量化实现）"""
        # 向上突破强度
        up_strength = np.where(
            df['close'] > df['bb_upper'],
            (df['close'] - df['bb_upper']) / df['bb_upper'] * df['volume_ratio'],
            0.0
        )

        # 向下突破强度
        down_strength = np.where(
            df['close'] < df['bb_lower'],
            (df['bb_lower'] - df['close']) / df['bb_lower'] * df['volume_ratio'],
            0.0
        )

        df['bb_breakout_strength'] = up_strength - down_strength  # 正值为向上，负值为向下

    def _calculate_walking_strength(self, df: pd.DataFrame):
        """计算贴轨强度（向量化实现）"""
        # 上轨贴轨强度
        upper_walking = 1 - abs(df['close'] - df['bb_upper']) / df['bb_upper']
        upper_walking = np.where(upper_walking > (1 - self.config.walking_tolerance), upper_walking, 0.0)

        # 下轨贴轨强度
        lower_walking = 1 - abs(df['close'] - df['bb_lower']) / df['bb_lower']
        lower_walking = np.where(lower_walking > (1 - self.config.walking_tolerance), lower_walking, 0.0)

        df['bb_walking_strength'] = upper_walking - lower_walking  # 正值为上轨贴轨，负值为下轨贴轨

    def _calculate_trend_alignment(self, df: pd.DataFrame):
        """计算趋势一致性（向量化实现）"""
        if self.config.trend_confirmation:
            # 价格趋势
            price_trend = df['close'].diff(5) / df['close'].shift(5)

            # 中轨趋势
            middle_trend = df['bb_middle_slope'] / df['bb_middle']

            # 趋势一致性：同向为正，反向为负
            df['bb_trend_alignment'] = np.sign(price_trend) * np.sign(middle_trend) * abs(middle_trend)
        else:
            df['bb_trend_alignment'] = 0.0

    def _calculate_volume_confirmation(self, df: pd.DataFrame):
        """计算成交量确认度（向量化实现）"""
        # 基于成交量比率的确认度
        df['bb_volume_confirmation'] = np.minimum(
            (df['volume_ratio'] - 1.0) / (self.config.breakout_volume_ratio - 1.0),
            2.0
        ).fillna(0.0)

    def _calculate_position_score(self, df: pd.DataFrame):
        """计算位置评分（向量化实现）"""
        # %B指标的非线性转换
        # 接近0或1时评分更高（接近极值）
        bb_percent_centered = abs(df['bb_percent'] - 0.5)
        df['bb_position_score'] = bb_percent_centered * 2  # 0.5时为0，0或1时为1

    def _calculate_momentum_score(self, df: pd.DataFrame):
        """计算动量评分（向量化实现）"""
        # 基于价格动量和BBW变化的综合评分
        price_momentum = df['close'].pct_change(5).rolling(3).mean()
        bbw_momentum = df['bbw'].pct_change(3).rolling(3).mean()

        df['bb_momentum_score'] = (abs(price_momentum) + abs(bbw_momentum)) / 2
        df['bb_momentum_score'] = df['bb_momentum_score'].fillna(0.0)

    def _feature_engineering(self, df: pd.DataFrame) -> pd.DataFrame:
        """特征工程（向量化实现）"""
        # 组合因子
        df['bb_composite_signal'] = (
            df['bb_squeeze_strength'] * 0.2 +
            df['bb_expansion_strength'] * 0.3 +
            abs(df['bb_breakout_strength']) * 0.2 +
            abs(df['bb_walking_strength']) * 0.1 +
            df['bb_trend_alignment'] * 0.1 +
            df['bb_volume_confirmation'] * 0.1
        )

        # 信号确认度
        df['bb_signal_confidence'] = (
            (df['bb_expansion_strength'] > 0).astype(float) +
            (abs(df['bb_breakout_strength']) > 0.1).astype(float) +
            (df['bb_volume_confirmation'] > 0.5).astype(float) +
            (abs(df['bb_trend_alignment']) > 0.1).astype(float)
        ) / 4.0

        # 相对强度（滚动排名）
        df['bb_relative_strength'] = df['bb_composite_signal'].rolling(20).rank(pct=True)

        # 波动率状态分类
        df['bb_volatility_regime'] = np.where(
            df['bbw_percentile'] < 0.2, 0,  # 低波动
            np.where(df['bbw_percentile'] > 0.8, 2, 1)  # 高波动 vs 正常波动
        )

        return df

    def _apply_smoothing_and_decay(self, df: pd.DataFrame) -> pd.DataFrame:
        """应用平滑和衰减（向量化实现）"""
        factor_cols = [col for col in df.columns if col.startswith('bb_')]

        for col in factor_cols:
            if col in ['bb_upper', 'bb_middle', 'bb_lower', 'bbw', 'bb_percent', 'bbw_percentile']:
                continue  # 跳过基础指标

            if self.config.smoothing_window > 1:
                df[col] = df[col].rolling(self.config.smoothing_window, center=True).mean().fillna(df[col])

            if self.config.strength_decay < 1.0:
                decay_weights = np.power(self.config.strength_decay, np.arange(len(df))[::-1])
                df[col] = df[col] * decay_weights

        return df

    def _normalize_factors(self, df: pd.DataFrame) -> pd.DataFrame:
        """标准化因子输出（向量化实现）"""
        factor_cols = [col for col in df.columns if col.startswith('bb_') and
                      col not in ['bb_upper', 'bb_middle', 'bb_lower', 'bbw', 'bb_percent', 'bbw_percentile']]

        for col in factor_cols:
            if col.endswith('_regime'):  # 跳过分类变量
                continue

            mean_val = df[col].rolling(252, min_periods=20).mean()
            std_val = df[col].rolling(252, min_periods=20).std()

            df[col + '_normalized'] = (df[col] - mean_val) / (std_val + 1e-8)
            df[col + '_normalized'] = df[col + '_normalized'].fillna(0)

        return df

    def _add_empty_factors(self, df: pd.DataFrame) -> pd.DataFrame:
        """添加空的因子列"""
        if df is None:
            return pd.DataFrame()

        df_result = df.copy()

        if self.config.output_mode == "boolean":
            boolean_factors = [
                'bb_squeeze_signal', 'bb_expansion_signal', 'bb_breakout_up', 'bb_breakout_down',
                'bb_walking_up', 'bb_walking_down', 'bb_middle_support', 'bb_middle_resistance'
            ]
            for name in boolean_factors:
                df_result[name] = False
        else:
            factor_names = [
                'bb_squeeze_strength', 'bb_expansion_strength', 'bb_breakout_strength',
                'bb_walking_strength', 'bb_trend_alignment', 'bb_volume_confirmation',
                'bb_position_score', 'bb_momentum_score'
            ]

            if self.config.feature_engineering:
                factor_names.extend([
                    'bb_composite_signal', 'bb_signal_confidence', 'bb_relative_strength', 'bb_volatility_regime'
                ])

            for name in factor_names:
                df_result[name] = 0.0
                if self.config.normalize_output and not name.endswith('_regime'):
                    df_result[name + '_normalized'] = 0.0

        return df_result

    def get_factor_names(self) -> List[str]:
        """获取因子名称列表"""
        if self.config.output_mode == "boolean":
            return [
                'bb_squeeze_signal', 'bb_expansion_signal', 'bb_breakout_up', 'bb_breakout_down',
                'bb_walking_up', 'bb_walking_down', 'bb_middle_support', 'bb_middle_resistance'
            ]
        else:
            base_names = [
                'bb_squeeze_strength', 'bb_expansion_strength', 'bb_breakout_strength',
                'bb_walking_strength', 'bb_trend_alignment', 'bb_volume_confirmation',
                'bb_position_score', 'bb_momentum_score'
            ]

            if self.config.feature_engineering:
                base_names.extend([
                    'bb_composite_signal', 'bb_signal_confidence', 'bb_relative_strength', 'bb_volatility_regime'
                ])

            if self.config.normalize_output:
                normalized_names = [name + '_normalized' for name in base_names if not name.endswith('_regime')]
                return base_names + normalized_names

            return base_names

    def get_factor_description(self) -> str:
        return f"""
高阶布林带因子

功能：基于波动率建模和趋势过滤的高阶布林带应用
参数：
- 布林带周期: {self.config.period}
- 标准差倍数: {self.config.std_dev}
- BBW回看周期: {self.config.bbw_lookback}
- 压缩阈值: {self.config.squeeze_threshold * 100:.1f}%分位数
- 扩张阈值: {self.config.expansion_threshold * 100:.1f}%分位数

核心特性：
1. 波动率压缩与扩张检测
   - BBW历史分位数分析
   - 压缩后扩张的趋势启动信号

2. 布林带突破真假判断
   - 成交量确认（{self.config.breakout_volume_ratio}倍放大）
   - 趋势方向验证
   - 假突破过滤

3. 贴轨效应量化分析
   - 连续{self.config.walking_days}天贴轨检测
   - 强势/弱势趋势识别

4. 中轨二次确认机制
   - 支撑/阻力功能验证
   - 趋势延续确认

输出模式：{self.config.output_mode}
- Boolean模式：8个布尔信号
- Continuous模式：8个基础因子 + 4个特征工程因子

因子值范围：
- 压缩/扩张强度: [0, 1]
- 突破/贴轨强度: [-1, 1] (正负表示方向)
- 趋势一致性: [-1, 1]
- 其他评分: [0, 1]

适用场景：
- 波动率建模和预测
- 趋势启动信号捕捉
- 假突破过滤
- 量化交易策略
- 机器学习特征工程
"""


# 向后兼容：保留原有BollingerSqueezeReleaseFactor接口
class BollingerSqueezeReleaseFactor(BollingerFactor):
    """
    布林带收缩释放因子（向后兼容）

    使用新的高阶布林带因子实现，但保持原有接口
    """

    def __init__(self, period: int = 20, std_dev: float = 2.0,
                 squeeze_threshold: float = 0.1, lookback_days: int = 10):
        # 转换为新配置
        config = BollingerConfig(
            period=period,
            std_dev=std_dev,
            squeeze_threshold=squeeze_threshold / 100,  # 转换为分位数
            output_mode="boolean"
        )
        # 使用不同的名称避免冲突
        BaseFactor.__init__(self, "bollinger_squeeze_release_factor", "technical")
        self.config = config

        # 保持原有属性以兼容
        self.period = period
        self.std_dev = std_dev
        self.squeeze_threshold = squeeze_threshold
        self.lookback_days = lookback_days

    def calculate(self, data: pd.DataFrame, **kwargs) -> Optional[FactorResult]:
        """
        计算布林带收缩释放因子（使用新的高阶实现）

        向后兼容原有接口，但使用新的高阶布林带因子实现
        """
        # 使用父类的高阶实现
        result = super().calculate(data, **kwargs)

        if isinstance(result, FactorResult):
            # 保持原有的因子名称
            result.factor_name = "bollinger_squeeze_release_factor"

            # 从新实现中提取收缩释放相关信息
            if hasattr(result, 'metadata') and 'signals' in result.metadata:
                signals = result.metadata['signals']
                squeeze_signal = signals.get('bb_squeeze_signal', False)
                expansion_signal = signals.get('bb_expansion_signal', False)

                # 转换为原有的逻辑
                if expansion_signal:
                    result.signal_strength = "STRONG"
                    result.factor_value = min(result.factor_value * 1.2, 1.0)
                elif squeeze_signal:
                    result.signal_strength = "MEDIUM"
                    result.factor_value = result.factor_value * 0.8

                # 添加向后兼容的元数据
                result.metadata.update({
                    'is_squeezed': squeeze_signal,
                    'is_releasing': expansion_signal,
                    'squeeze_threshold': self.squeeze_threshold,
                    'lookback_days': self.lookback_days
                })

        return result

    def get_factor_description(self) -> str:
        return f"""
布林带收缩释放因子（高阶版本）

功能：基于高阶布林带因子的收缩释放检测
参数：
- 周期: {self.period}
- 标准差倍数: {self.std_dev}
- 收缩阈值: {self.squeeze_threshold * 100:.1f}%
- 回看天数: {self.lookback_days}

新特性：
1. BBW历史分位数分析
2. 成交量确认机制
3. 趋势方向验证
4. 假突破过滤

向后兼容：
- 保持原有接口不变
- 使用新的高阶算法
- 提供更准确的信号

信号强度：
- STRONG: 波动率扩张信号
- MEDIUM: 波动率压缩信号
- WEAK/NONE: 其他情况
"""
