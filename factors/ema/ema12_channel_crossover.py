#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EMA12通道穿越因子

基于EMA12与EMA144/169通道的穿越分析，识别潜在的趋势信号。
支持多种输出模式和使用场景，通过配置文件统一管理参数。

核心特性：
1. 配置文件驱动的参数管理
2. 支持布尔信号和连续数值两种输出模式
3. Qlib兼容性（可选）
4. 参数优化支持
5. 高性能向量化计算
6. 统一的EMA因子架构

EMA通道说明：
- EMA12：快速均线，用于捕捉短期趋势
- EMA144/169：慢速均线组成的通道，用于确定中长期趋势方向
- 通道中轴：(EMA144 + EMA169) / 2

穿越检测逻辑：
1. 上穿信号：EMA12从下方穿越通道上轨或中轴（看涨信号）
2. 下跌转头：EMA12下跌至通道附近后转头向上（看涨信号）
3. 通道支撑：EMA12在通道内获得支撑并反弹
4. 趋势确认：结合成交量和价格行为确认信号强度

作者: QuantFM Team
创建时间: 2025-08-31
"""

import pandas as pd
import numpy as np
import yaml
import os
from typing import Dict, List, Tuple, Optional, Union, Any
from dataclasses import dataclass

# 使用项目标准模块
from utils.logger import get_logger
from indicators.talib_wrapper import calculate_ema_series
from factors.common_utils import (
    calculate_ema_turnaround_factors,
    detect_ema_turnaround,
    detect_price_oscillation
)
from ..base_factor import BaseFactor

@dataclass
class EMA12ChannelConfig:
    """EMA12通道穿越因子配置类"""
    # EMA参数
    fast_period: int = 12          # 快速EMA周期
    slow_period1: int = 144        # 慢速EMA1周期
    slow_period2: int = 169        # 慢速EMA2周期
    
    # 穿越检测参数
    crossover_threshold: float = 0.002    # 穿越确认阈值（0.2%）
    channel_width_threshold: float = 0.01 # 通道宽度阈值（1%）
    turnaround_lookback: int = 5          # 转头检测回看周期
    turnaround_threshold: float = 0.005   # 转头确认阈值（0.5%）
    
    # 信号强度参数
    volume_confirmation: bool = True      # 是否需要成交量确认
    volume_ratio_threshold: float = 1.2   # 成交量放大倍数
    price_momentum_weight: float = 0.4    # 价格动量权重
    volume_momentum_weight: float = 0.3   # 成交量动量权重
    ema_momentum_weight: float = 0.3      # EMA动量权重
    
    # 输出控制
    output_mode: str = "continuous"       # "boolean" | "continuous"
    normalize_output: bool = True
    feature_engineering: bool = True
    
    # 性能优化
    smoothing_window: int = 3
    strength_decay: float = 0.95
    enable_cache: bool = True
    
    # Qlib集成
    enable_qlib_mode: bool = False
    auto_optimization: bool = False
    ml_ready: bool = True

    @classmethod
    def from_config_file(cls, config_path: str = None, scenario: str = "production") -> 'EMA12ChannelConfig':
        """从配置文件加载配置"""
        if config_path is None:
            # 默认配置文件路径
            current_dir = os.path.dirname(os.path.abspath(__file__))
            config_path = os.path.join(os.path.dirname(os.path.dirname(current_dir)),
                                     "config", "ema_params.yaml")

        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)

            # 获取EMA12通道因子配置
            ema_config = config_data.get('ema12_channel_crossover', {})

            # 如果指定了场景，使用场景配置覆盖
            if scenario and scenario in config_data.get('scenarios', {}):
                scenario_config = config_data['scenarios'][scenario].get('ema12_channel_crossover', {})
                ema_config = cls._merge_configs(ema_config, scenario_config)

            # 展平嵌套配置
            flat_config = {}
            for section, params in ema_config.items():
                if section == 'param_space':
                    # 跳过参数空间定义，不作为实际参数
                    continue
                elif isinstance(params, dict):
                    flat_config.update(params)
                else:
                    flat_config[section] = params

            return cls(**flat_config)

        except Exception as e:
            logging.warning(f"配置文件加载失败，使用默认配置: {e}")
            return cls()

    @staticmethod
    def _merge_configs(base_config: dict, override_config: dict) -> dict:
        """合并配置"""
        merged = base_config.copy()
        for key, value in override_config.items():
            if isinstance(value, dict) and key in merged:
                merged[key].update(value)
            else:
                merged[key] = value
        return merged

    def get_param_space(self) -> Dict[str, Dict]:
        """获取参数空间（用于优化）"""
        return {
            'fast_period': {'type': 'int', 'range': [8, 20], 'default': self.fast_period},
            'slow_period1': {'type': 'int', 'range': [120, 180], 'default': self.slow_period1},
            'slow_period2': {'type': 'int', 'range': [150, 200], 'default': self.slow_period2},
            'crossover_threshold': {'type': 'float', 'range': [0.001, 0.01], 'default': self.crossover_threshold},
            'channel_width_threshold': {'type': 'float', 'range': [0.005, 0.02], 'default': self.channel_width_threshold},
            'turnaround_lookback': {'type': 'int', 'range': [3, 10], 'default': self.turnaround_lookback},
            'turnaround_threshold': {'type': 'float', 'range': [0.002, 0.01], 'default': self.turnaround_threshold},
            'volume_ratio_threshold': {'type': 'float', 'range': [1.0, 2.0], 'default': self.volume_ratio_threshold},
            'smoothing_window': {'type': 'int', 'range': [1, 5], 'default': self.smoothing_window},
            'strength_decay': {'type': 'float', 'range': [0.8, 0.99], 'default': self.strength_decay}
        }


class EMA12ChannelCrossoverFactor(BaseFactor):
    """
    EMA12通道穿越因子

    支持多种输出模式和使用场景的EMA通道穿越因子实现。
    完全兼容原有功能，包括Qlib支持、参数优化等。
    """

    def __init__(self, config: EMA12ChannelConfig = None, scenario: str = "production", **kwargs):
        """
        初始化EMA12通道穿越因子

        Args:
            config: 因子配置对象
            scenario: 使用场景 ("development", "production", "qlib_training")
            **kwargs: 直接参数覆盖
        """
        # 配置初始化
        if config is None:
            config = EMA12ChannelConfig.from_config_file(scenario=scenario)

        # 参数覆盖
        for key, value in kwargs.items():
            if hasattr(config, key):
                setattr(config, key, value)

        super().__init__("ema12_channel_crossover", "ema")

        # 使用项目统一日志模块
        self.logger = get_logger('ema12_channel_crossover')

        self.config = config
        self.scenario = scenario

        # 因子缓存
        self._factor_cache = {} if self.config.enable_cache else None

        # 根据配置决定计算方法
        if self.config.output_mode == "boolean":
            self._calculate_method = self._calculate_boolean_factors
        else:
            self._calculate_method = self._calculate_continuous_factors

    def calculate(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        计算EMA12通道穿越因子

        Args:
            df: 包含OHLCV数据的DataFrame

        Returns:
            添加了穿越因子的DataFrame
        """
        try:
            # 检查输入数据
            if df is None:
                self.logger.error("输入数据为None")
                return pd.DataFrame()

            min_length = max(self.config.slow_period1, self.config.slow_period2) + self.config.turnaround_lookback * 2
            if len(df) < min_length:
                self.logger.warning(f"数据长度不足，需要至少{min_length}条数据")
                return self._add_empty_factors(df)

            df_result = df.copy()

            # 1. 计算EMA指标
            df_result = self._calculate_ema_indicators(df_result)

            # 2. 根据配置选择计算方法
            df_result = self._calculate_method(df_result)

            # 3. 后处理
            if self.config.normalize_output and self.config.output_mode == "continuous":
                df_result = self._normalize_factors(df_result)

            return df_result

        except Exception as e:
            self.logger.error(f"计算EMA12通道穿越因子失败: {e}")
            return self._add_empty_factors(df)

    def _calculate_ema_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """使用项目标准EMA计算模块"""
        try:
            # 使用标准EMA计算模块进行批量计算
            ema_periods = [self.config.fast_period, self.config.slow_period1, self.config.slow_period2]
            ema_df = calculate_ema_series(df, ema_periods)

            # 合并EMA结果到原DataFrame
            result_df = df.copy()
            for period in ema_periods:
                ema_col = f'ema_{period}'
                if ema_col in ema_df.columns:
                    result_df[ema_col] = ema_df[ema_col]

            # 为了保持向后兼容，创建别名
            result_df['ema12'] = result_df[f'ema_{self.config.fast_period}']
            result_df['ema144'] = result_df[f'ema_{self.config.slow_period1}']
            result_df['ema169'] = result_df[f'ema_{self.config.slow_period2}']

            # 计算通道（使用优化的向量化计算）
            result_df['channel_upper'] = np.maximum(result_df['ema144'], result_df['ema169'])
            result_df['channel_lower'] = np.minimum(result_df['ema144'], result_df['ema169'])
            result_df['channel_mid'] = (result_df['ema144'] + result_df['ema169']) / 2

            # 安全的通道宽度计算（避免除零）
            with np.errstate(divide='ignore', invalid='ignore'):
                result_df['channel_width'] = np.where(
                    result_df['channel_mid'] != 0,
                    (result_df['channel_upper'] - result_df['channel_lower']) / result_df['channel_mid'],
                    0
                )

            # 安全的相对位置计算
            channel_range = result_df['channel_upper'] - result_df['channel_lower']
            with np.errstate(divide='ignore', invalid='ignore'):
                result_df['ema12_channel_position'] = np.where(
                    channel_range != 0,
                    (result_df['ema12'] - result_df['channel_lower']) / channel_range,
                    0.5  # 默认中间位置
                )

                result_df['ema12_to_mid_distance'] = np.where(
                    result_df['channel_mid'] != 0,
                    (result_df['ema12'] - result_df['channel_mid']) / result_df['channel_mid'],
                    0
                )

            self.logger.debug(f"EMA指标计算完成，包含{len(ema_periods)}个周期")
            return result_df

        except Exception as e:
            self.logger.error(f"计算EMA指标失败: {e}")
            # 降级到原始实现
            return self._calculate_ema_indicators_fallback(df)

    def _calculate_ema_indicators_fallback(self, df: pd.DataFrame) -> pd.DataFrame:
        """降级EMA计算方法（当标准模块不可用时）"""
        try:
            import talib

            # 使用talib直接计算
            df['ema12'] = talib.EMA(df['close'].values, timeperiod=self.config.fast_period)
            df['ema144'] = talib.EMA(df['close'].values, timeperiod=self.config.slow_period1)
            df['ema169'] = talib.EMA(df['close'].values, timeperiod=self.config.slow_period2)

            # 计算通道
            df['channel_upper'] = np.maximum(df['ema144'], df['ema169'])
            df['channel_lower'] = np.minimum(df['ema144'], df['ema169'])
            df['channel_mid'] = (df['ema144'] + df['ema169']) / 2
            df['channel_width'] = (df['channel_upper'] - df['channel_lower']) / df['channel_mid']

            # 计算EMA12相对通道位置
            df['ema12_channel_position'] = (df['ema12'] - df['channel_lower']) / (df['channel_upper'] - df['channel_lower'])
            df['ema12_to_mid_distance'] = (df['ema12'] - df['channel_mid']) / df['channel_mid']

            self.logger.warning("使用降级EMA计算方法")
            return df

        except Exception as e:
            self.logger.error(f"降级EMA计算也失败: {e}")
            return df

    def _calculate_boolean_factors(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算布尔信号因子（策略信号模式）"""
        # 初始化因子列
        df['ema12_crossover_upper'] = False    # EMA12上穿通道上轨
        df['ema12_crossover_mid'] = False      # EMA12上穿通道中轴
        df['ema12_turnaround_up'] = False      # EMA12转头向上
        df['ema12_channel_support'] = False    # EMA12获得通道支撑
        
        # 检测穿越信号
        df = self._detect_crossover_signals(df)
        
        # 检测转头信号
        df = self._detect_turnaround_signals(df)
        
        # 检测支撑信号
        df = self._detect_support_signals(df)
        
        return df

    def _calculate_continuous_factors(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算连续数值因子（机器学习模式）"""
        # 计算基础强度因子
        df['ema12_crossover_strength'] = 0.0
        df['ema12_turnaround_strength'] = 0.0
        df['ema12_momentum_strength'] = 0.0
        df['ema12_channel_strength'] = 0.0
        
        # 计算各种强度
        df = self._calculate_crossover_strength(df)
        df = self._calculate_turnaround_strength(df)
        df = self._calculate_momentum_strength(df)
        df = self._calculate_channel_strength(df)
        
        # 计算综合强度
        df['ema12_total_strength'] = (
            df['ema12_crossover_strength'] * 0.3 +
            df['ema12_turnaround_strength'] * 0.3 +
            df['ema12_momentum_strength'] * 0.2 +
            df['ema12_channel_strength'] * 0.2
        )
        
        return df

    def _detect_crossover_signals(self, df: pd.DataFrame) -> pd.DataFrame:
        """检测穿越信号"""
        # 上穿通道上轨
        crossover_upper = (
            (df['ema12'].shift(1) <= df['channel_upper'].shift(1)) &
            (df['ema12'] > df['channel_upper']) &
            (df['ema12'] - df['channel_upper'] > df['channel_upper'] * self.config.crossover_threshold)
        )
        df['ema12_crossover_upper'] = crossover_upper

        # 上穿通道中轴
        crossover_mid = (
            (df['ema12'].shift(1) <= df['channel_mid'].shift(1)) &
            (df['ema12'] > df['channel_mid']) &
            (df['ema12'] - df['channel_mid'] > df['channel_mid'] * self.config.crossover_threshold)
        )
        df['ema12_crossover_mid'] = crossover_mid

        return df

    def _detect_turnaround_signals(self, df: pd.DataFrame) -> pd.DataFrame:
        """使用项目标准拐头检测模块"""
        try:
            # 使用标准拐头检测模块
            ema_column = 'ema12'
            if ema_column in df.columns:
                # 调用标准拐头检测函数
                turnaround_results = detect_ema_turnaround(
                    df[ema_column],
                    lookback_period=self.config.turnaround_lookback,
                    threshold=self.config.turnaround_threshold
                )

                # 处理检测结果
                if isinstance(turnaround_results, dict):
                    # 如果返回字典格式
                    df['ema12_turnaround_up'] = turnaround_results.get('turnaround_signals', False)
                elif isinstance(turnaround_results, pd.Series):
                    # 如果返回Series格式
                    df['ema12_turnaround_up'] = turnaround_results
                else:
                    # 降级到手工实现
                    df = self._detect_turnaround_signals_fallback(df)

                # 结合通道位置进行过滤
                if 'ema12_turnaround_up' in df.columns:
                    # 只保留在通道附近的转头信号
                    near_channel_mask = (
                        (df['ema12_channel_position'] < 0.3) |  # 在通道下方
                        (abs(df['ema12_to_mid_distance']) < self.config.channel_width_threshold)
                    )
                    df['ema12_turnaround_up'] = df['ema12_turnaround_up'] & near_channel_mask

                self.logger.debug("使用标准拐头检测模块完成")
            else:
                self.logger.warning(f"未找到{ema_column}列，跳过拐头检测")
                df['ema12_turnaround_up'] = False

            return df

        except Exception as e:
            self.logger.warning(f"标准拐头检测失败，使用降级方法: {e}")
            return self._detect_turnaround_signals_fallback(df)

    def _detect_turnaround_signals_fallback(self, df: pd.DataFrame) -> pd.DataFrame:
        """降级拐头检测方法"""
        try:
            lookback = self.config.turnaround_lookback

            # 计算EMA12的变化率
            df['ema12_change'] = df['ema12'].pct_change()
            df['ema12_slope'] = df['ema12_change'].rolling(window=3).mean()

            # 初始化信号列
            df['ema12_turnaround_up'] = False

            # 向量化检测转头条件
            if len(df) > lookback:
                # 计算滚动最小斜率（前期下跌检测）
                recent_decline = df['ema12_slope'].rolling(window=lookback).min() < -self.config.turnaround_threshold

                # 当前转头检测
                current_turnaround = (
                    (df['ema12_slope'] > 0) &
                    (df['ema12_slope'] > df['ema12_slope'].shift(1))
                )

                # 接近通道检测
                near_channel = (
                    (df['ema12_channel_position'] < 0.3) |
                    (abs(df['ema12_to_mid_distance']) < self.config.channel_width_threshold)
                )

                # 综合条件
                df['ema12_turnaround_up'] = recent_decline & current_turnaround & near_channel

            self.logger.debug("使用降级拐头检测方法完成")
            return df

        except Exception as e:
            self.logger.error(f"降级拐头检测失败: {e}")
            df['ema12_turnaround_up'] = False
            return df

    def _detect_support_signals(self, df: pd.DataFrame) -> pd.DataFrame:
        """检测通道支撑信号"""
        # 确保ema12_slope已计算
        if 'ema12_slope' not in df.columns:
            df['ema12_change'] = df['ema12'].pct_change()
            df['ema12_slope'] = df['ema12_change'].rolling(window=3).mean()

        # 检测EMA12在通道内获得支撑
        support_condition = (
            (df['ema12_channel_position'] > 0.1) &  # 在通道内
            (df['ema12_channel_position'] < 0.9) &
            (df['ema12_slope'] > 0) &  # 向上
            (df['ema12_slope'].shift(1) <= 0)  # 前期向下或平
        )
        df['ema12_channel_support'] = support_condition

        return df

    def _calculate_crossover_strength(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算穿越强度"""
        # 基于穿越幅度和速度计算强度
        upper_strength = np.where(
            df['ema12'] > df['channel_upper'],
            (df['ema12'] - df['channel_upper']) / df['channel_upper'],
            0
        )

        mid_strength = np.where(
            df['ema12'] > df['channel_mid'],
            (df['ema12'] - df['channel_mid']) / df['channel_mid'] * 0.5,  # 中轴穿越权重较低
            0
        )

        df['ema12_crossover_strength'] = np.maximum(upper_strength, mid_strength)

        # 应用衰减
        if self.config.smoothing_window > 1:
            df['ema12_crossover_strength'] = df['ema12_crossover_strength'].rolling(
                window=self.config.smoothing_window
            ).mean().fillna(0)

        return df

    def _calculate_turnaround_strength(self, df: pd.DataFrame) -> pd.DataFrame:
        """使用项目标准模块计算转头强度"""
        try:
            # 尝试使用标准EMA拐头因子计算
            ema_periods = [self.config.fast_period]
            turnaround_factors = calculate_ema_turnaround_factors(
                df,
                ema_periods=ema_periods,
                lookback=self.config.turnaround_lookback
            )

            # 提取转头强度
            if f'ema_{self.config.fast_period}_turnaround_strength' in turnaround_factors.columns:
                df['ema12_turnaround_strength'] = turnaround_factors[f'ema_{self.config.fast_period}_turnaround_strength']
            else:
                # 降级到手工计算
                df = self._calculate_turnaround_strength_fallback(df)

            # 应用位置权重修正
            if 'ema12_channel_position' in df.columns:
                position_weight = np.where(
                    df['ema12_channel_position'] < 0.5,
                    1.0 - df['ema12_channel_position'],
                    0.5
                )
                df['ema12_turnaround_strength'] = df['ema12_turnaround_strength'] * position_weight

            self.logger.debug("使用标准模块计算转头强度完成")
            return df

        except Exception as e:
            self.logger.warning(f"标准转头强度计算失败，使用降级方法: {e}")
            return self._calculate_turnaround_strength_fallback(df)

    def _calculate_turnaround_strength_fallback(self, df: pd.DataFrame) -> pd.DataFrame:
        """降级转头强度计算方法"""
        try:
            # 确保ema12_slope已计算
            if 'ema12_slope' not in df.columns:
                df['ema12_change'] = df['ema12'].pct_change()
                df['ema12_slope'] = df['ema12_change'].rolling(window=3).mean()

            # 基于斜率变化计算转头强度
            with np.errstate(divide='ignore', invalid='ignore'):
                slope_strength = np.where(
                    df['ema12_slope'] > 0,
                    np.clip(df['ema12_slope'] / self.config.turnaround_threshold, 0, 5),  # 限制最大值
                    0
                )

            # 位置修正：越接近通道下方，转头信号越强
            position_weight = np.where(
                df['ema12_channel_position'] < 0.5,
                1.0 - df['ema12_channel_position'],
                0.5
            )

            df['ema12_turnaround_strength'] = slope_strength * position_weight

            self.logger.debug("使用降级方法计算转头强度完成")
            return df

        except Exception as e:
            self.logger.error(f"降级转头强度计算失败: {e}")
            df['ema12_turnaround_strength'] = 0.0
            return df

    def _calculate_momentum_strength(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算动量强度"""
        # 价格动量
        price_momentum = df['close'].pct_change(5)  # 5日价格变化

        # 成交量动量（如果启用成交量确认）
        if self.config.volume_confirmation and 'volume' in df.columns:
            volume_ma = df['volume'].rolling(window=20).mean()
            volume_momentum = df['volume'] / volume_ma
            volume_strength = np.where(
                volume_momentum > self.config.volume_ratio_threshold,
                (volume_momentum - 1) / self.config.volume_ratio_threshold,
                0
            )
        else:
            volume_strength = 0

        # EMA动量
        ema_momentum = df['ema12'].pct_change(3)  # 3日EMA变化

        # 综合动量强度
        df['ema12_momentum_strength'] = (
            price_momentum * self.config.price_momentum_weight +
            volume_strength * self.config.volume_momentum_weight +
            ema_momentum * self.config.ema_momentum_weight
        ).fillna(0)

        return df

    def _calculate_channel_strength(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算通道强度"""
        # 基于通道宽度和EMA12在通道中的位置
        channel_stability = 1.0 / (1.0 + df['channel_width'])  # 通道越窄越稳定

        # 位置强度：在通道下方时强度更高
        position_strength = np.where(
            df['ema12_channel_position'] < 0.5,
            1.0 - df['ema12_channel_position'],
            0.5 - abs(df['ema12_channel_position'] - 0.5)
        )

        df['ema12_channel_strength'] = channel_stability * position_strength

        return df

    def _add_empty_factors(self, df: pd.DataFrame) -> pd.DataFrame:
        """添加空因子列"""
        if self.config.output_mode == "boolean":
            df['ema12_crossover_upper'] = False
            df['ema12_crossover_mid'] = False
            df['ema12_turnaround_up'] = False
            df['ema12_channel_support'] = False
        else:
            df['ema12_crossover_strength'] = 0.0
            df['ema12_turnaround_strength'] = 0.0
            df['ema12_momentum_strength'] = 0.0
            df['ema12_channel_strength'] = 0.0
            df['ema12_total_strength'] = 0.0

        return df

    def _normalize_factors(self, df: pd.DataFrame) -> pd.DataFrame:
        """标准化因子值"""
        factor_columns = [col for col in df.columns if 'ema12_' in col and '_strength' in col]

        for col in factor_columns:
            if col in df.columns:
                # 使用滚动标准化
                rolling_mean = df[col].rolling(window=252, min_periods=20).mean()
                rolling_std = df[col].rolling(window=252, min_periods=20).std()
                df[col] = (df[col] - rolling_mean) / (rolling_std + 1e-8)
                df[col] = df[col].fillna(0)

        return df

    def get_factor_names(self) -> List[str]:
        """获取因子名称列表"""
        if self.config.output_mode == "boolean":
            return [
                'ema12_crossover_upper',
                'ema12_crossover_mid',
                'ema12_turnaround_up',
                'ema12_channel_support'
            ]
        else:
            return [
                'ema12_crossover_strength',
                'ema12_turnaround_strength',
                'ema12_momentum_strength',
                'ema12_channel_strength',
                'ema12_total_strength'
            ]

    def get_factor_description(self) -> Dict[str, str]:
        """获取因子描述"""
        if self.config.output_mode == "boolean":
            return {
                'ema12_crossover_upper': 'EMA12上穿通道上轨信号',
                'ema12_crossover_mid': 'EMA12上穿通道中轴信号',
                'ema12_turnaround_up': 'EMA12转头向上信号',
                'ema12_channel_support': 'EMA12获得通道支撑信号'
            }
        else:
            return {
                'ema12_crossover_strength': 'EMA12穿越强度',
                'ema12_turnaround_strength': 'EMA12转头强度',
                'ema12_momentum_strength': 'EMA12动量强度',
                'ema12_channel_strength': 'EMA12通道强度',
                'ema12_total_strength': 'EMA12综合强度'
            }

    def optimize_parameters(self, df: pd.DataFrame, target_column: str = 'returns_5d') -> Dict[str, Any]:
        """参数优化"""
        if not self.config.auto_optimization:
            return {}

        # 这里可以实现参数优化逻辑
        # 返回最优参数组合
        return {}

    def get_qlib_expression(self) -> str:
        """获取Qlib表达式（如果启用Qlib模式）"""
        if not self.config.enable_qlib_mode:
            return ""

        # 构造Qlib兼容的表达式
        return f"""
        # EMA12通道穿越因子
        ema12 = EMA($close, {self.config.fast_period})
        ema144 = EMA($close, {self.config.slow_period1})
        ema169 = EMA($close, {self.config.slow_period2})
        channel_upper = Max(ema144, ema169)
        channel_lower = Min(ema144, ema169)
        channel_mid = (ema144 + ema169) / 2

        # 穿越信号
        crossover_upper = (Ref(ema12, 1) <= Ref(channel_upper, 1)) & (ema12 > channel_upper)
        crossover_mid = (Ref(ema12, 1) <= Ref(channel_mid, 1)) & (ema12 > channel_mid)

        # 转头信号
        ema12_slope = (ema12 / Ref(ema12, 3) - 1)
        turnaround = (ema12_slope > 0) & (Ref(ema12_slope, 1) <= 0)
        """

    def get_feature_importance(self) -> Dict[str, float]:
        """获取特征重要性"""
        if self.config.output_mode == "boolean":
            return {
                'ema12_crossover_upper': 0.35,
                'ema12_crossover_mid': 0.25,
                'ema12_turnaround_up': 0.25,
                'ema12_channel_support': 0.15
            }
        else:
            return {
                'ema12_crossover_strength': 0.30,
                'ema12_turnaround_strength': 0.30,
                'ema12_momentum_strength': 0.20,
                'ema12_channel_strength': 0.20
            }

    def validate_signals(self, df: pd.DataFrame) -> Dict[str, Any]:
        """验证信号质量"""
        validation_results = {
            'total_signals': 0,
            'signal_frequency': 0.0,
            'avg_signal_strength': 0.0,
            'signal_distribution': {},
            'data_quality': 'good'
        }

        try:
            factor_names = self.get_factor_names()

            if self.config.output_mode == "boolean":
                # 布尔信号验证
                total_signals = sum(df[col].sum() for col in factor_names if col in df.columns)
                validation_results['total_signals'] = total_signals
                validation_results['signal_frequency'] = total_signals / len(df) if len(df) > 0 else 0

                # 信号分布
                for col in factor_names:
                    if col in df.columns:
                        validation_results['signal_distribution'][col] = df[col].sum()
            else:
                # 连续信号验证
                strength_cols = [col for col in factor_names if '_strength' in col]
                if strength_cols:
                    avg_strength = df[strength_cols].mean().mean()
                    validation_results['avg_signal_strength'] = avg_strength

                    # 强度分布
                    for col in strength_cols:
                        if col in df.columns:
                            validation_results['signal_distribution'][col] = {
                                'mean': df[col].mean(),
                                'std': df[col].std(),
                                'max': df[col].max(),
                                'min': df[col].min()
                            }

            return validation_results

        except Exception as e:
            validation_results['data_quality'] = f'error: {e}'
            return validation_results


# 兼容性函数和类别名
EMA12ChannelCrossover = EMA12ChannelCrossoverFactor  # 别名兼容

def create_ema12_channel_factor(scenario: str = "production", **kwargs) -> EMA12ChannelCrossoverFactor:
    """工厂函数：创建EMA12通道穿越因子"""
    return EMA12ChannelCrossoverFactor(scenario=scenario, **kwargs)

def get_default_config() -> EMA12ChannelConfig:
    """获取默认配置"""
    return EMA12ChannelConfig()

# 导出接口
__all__ = [
    'EMA12ChannelConfig',
    'EMA12ChannelCrossoverFactor',
    'EMA12ChannelCrossover',
    'create_ema12_channel_factor',
    'get_default_config'
]
