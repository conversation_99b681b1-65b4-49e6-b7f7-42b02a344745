#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
斐波那契因子

检测价格是否接近斐波那契回撤和扩展位
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any
from datetime import datetime

from .base_factor import BaseFactor, FactorResult
try:
    from indicators.fibonacci_comprehensive import calculate_fibonacci_levels_enhanced
except ImportError:
    # 如果indicators模块不可用，提供占位函数
    def calculate_fibonacci_levels_enhanced(*args, **kwargs):
        return {}


class FibonacciFactor(BaseFactor):
    """斐波那契因子"""
    
    def __init__(self, lookback_period: int = 252, price_threshold: float = 0.005):
        super().__init__("fibonacci_factor", "technical")
        
        self.lookback_period = lookback_period  # 计算斐波那契位的回看周期
        self.price_threshold = price_threshold  # 价格接近阈值
        self.required_columns = ['high', 'low', 'close']
        self.min_data_length = max(50, lookback_period // 4)
    
    def calculate(self, data: pd.DataFrame, **kwargs) -> Optional[FactorResult]:
        """计算斐波那契因子"""
        try:
            current_price = float(data['close'].iloc[-1])
            
            # 计算斐波那契位
            fib_levels = calculate_fibonacci_levels_enhanced(data, self.lookback_period)
            
            if not fib_levels:
                return None
            
            # 检测价格是否接近任何斐波那契位
            near_levels = []
            
            for fib_name, fib_value in fib_levels.items():
                if fib_value > 0:
                    deviation = abs(current_price - fib_value) / fib_value
                    if deviation <= self.price_threshold:
                        near_levels.append({
                            'name': fib_name,
                            'value': fib_value,
                            'deviation': deviation,
                            'distance_score': 1 - deviation / self.price_threshold
                        })
            
            if not near_levels:
                return FactorResult(
                    factor_name=self.factor_name,
                    factor_value=0.0,
                    factor_type=self.factor_type,
                    confidence=0.0,
                    signal_strength="NONE",
                    metadata={'fib_levels': fib_levels, 'near_levels': []}
                )
            
            # 找到最接近的斐波那契位
            best_level = max(near_levels, key=lambda x: x['distance_score'])
            
            # 计算因子值（基于接近程度）
            factor_value = best_level['distance_score']
            
            # 计算置信度（考虑多个位的重合）
            confidence = min(len(near_levels) * 0.3 + factor_value * 0.7, 1.0)
            
            # 确定信号强度
            if factor_value > 0.8 and len(near_levels) >= 2:
                signal_strength = "STRONG"
            elif factor_value > 0.6:
                signal_strength = "MEDIUM"
            elif factor_value > 0.3:
                signal_strength = "WEAK"
            else:
                signal_strength = "NONE"
            
            metadata = {
                'fib_levels': fib_levels,
                'near_levels': near_levels,
                'best_level': best_level,
                'current_price': current_price,
                'lookback_period': self.lookback_period,
                'price_threshold': self.price_threshold
            }
            
            return FactorResult(
                factor_name=self.factor_name,
                factor_value=factor_value,
                factor_type=self.factor_type,
                confidence=confidence,
                signal_strength=signal_strength,
                metadata=metadata
            )
            
        except Exception as e:
            self.logger.error(f"计算斐波那契因子失败: {e}")
            return None
    
    def get_factor_description(self) -> str:
        return f"""
斐波那契因子

功能：检测价格是否接近斐波那契回撤和扩展位
参数：
- 回看周期: {self.lookback_period} 天
- 价格阈值: {self.price_threshold * 100:.1f}%

斐波那契位：
- 回撤位: 23.6%, 38.2%, 50%, 61.8%, 78.6%
- 扩展位: 127.2%, 138.2%, 161.8%

因子值：基于价格与最近斐波那契位的接近程度 (0-1)
信号强度：
- STRONG: 因子值>0.8且接近多个位
- MEDIUM: 因子值>0.6
- WEAK: 因子值>0.3
- NONE: 因子值≤0.3
"""


class MultiFibonacciFactor(BaseFactor):
    """多时间周期斐波那契因子"""
    
    def __init__(self, periods: List[int] = None, price_threshold: float = 0.005):
        super().__init__("multi_fibonacci_factor", "technical")
        
        self.periods = periods or [63, 126, 252]  # 3个月、6个月、1年
        self.price_threshold = price_threshold
        self.required_columns = ['high', 'low', 'close']
        self.min_data_length = max(self.periods) if self.periods else 252
    
    def calculate(self, data: pd.DataFrame, **kwargs) -> Optional[FactorResult]:
        """计算多时间周期斐波那契因子"""
        try:
            current_price = float(data['close'].iloc[-1])
            
            all_near_levels = []
            period_results = {}
            
            # 计算每个时间周期的斐波那契位
            for period in self.periods:
                if len(data) < period:
                    continue
                
                fib_levels = calculate_fibonacci_levels_enhanced(data, period)
                if not fib_levels:
                    continue
                
                near_levels = []
                for fib_name, fib_value in fib_levels.items():
                    if fib_value > 0:
                        deviation = abs(current_price - fib_value) / fib_value
                        if deviation <= self.price_threshold:
                            level_info = {
                                'name': fib_name,
                                'value': fib_value,
                                'deviation': deviation,
                                'distance_score': 1 - deviation / self.price_threshold,
                                'period': period
                            }
                            near_levels.append(level_info)
                            all_near_levels.append(level_info)
                
                period_results[f'period_{period}'] = {
                    'fib_levels': fib_levels,
                    'near_levels': near_levels
                }
            
            if not all_near_levels:
                return FactorResult(
                    factor_name=self.factor_name,
                    factor_value=0.0,
                    factor_type=self.factor_type,
                    confidence=0.0,
                    signal_strength="NONE",
                    metadata={'period_results': period_results}
                )
            
            # 计算综合因子值
            # 考虑：1) 最佳接近程度 2) 重合数量 3) 时间周期权重
            best_score = max(level['distance_score'] for level in all_near_levels)
            overlap_bonus = min(len(all_near_levels) * 0.1, 0.5)  # 重合奖励
            
            # 长期周期权重更高
            period_weights = {period: 1 + i * 0.2 for i, period in enumerate(sorted(self.periods))}
            weighted_score = sum(
                level['distance_score'] * period_weights.get(level['period'], 1.0)
                for level in all_near_levels
            ) / len(all_near_levels)
            
            factor_value = min(best_score * 0.6 + weighted_score * 0.3 + overlap_bonus, 1.0)
            
            # 计算置信度
            confidence = min(len(all_near_levels) * 0.2 + factor_value * 0.8, 1.0)
            
            # 确定信号强度
            if factor_value > 0.8 and len(all_near_levels) >= 3:
                signal_strength = "STRONG"
            elif factor_value > 0.6 and len(all_near_levels) >= 2:
                signal_strength = "MEDIUM"
            elif factor_value > 0.3:
                signal_strength = "WEAK"
            else:
                signal_strength = "NONE"
            
            metadata = {
                'period_results': period_results,
                'all_near_levels': all_near_levels,
                'best_score': best_score,
                'overlap_count': len(all_near_levels),
                'current_price': current_price,
                'periods': self.periods
            }
            
            return FactorResult(
                factor_name=self.factor_name,
                factor_value=factor_value,
                factor_type=self.factor_type,
                confidence=confidence,
                signal_strength=signal_strength,
                metadata=metadata
            )
            
        except Exception as e:
            self.logger.error(f"计算多时间周期斐波那契因子失败: {e}")
            return None
    
    def get_factor_description(self) -> str:
        return f"""
多时间周期斐波那契因子

功能：检测价格在多个时间周期斐波那契位的重合情况
时间周期：{self.periods} 天
价格阈值：{self.price_threshold * 100:.1f}%

计算方法：
1. 计算每个时间周期的斐波那契位
2. 检测价格接近程度
3. 综合考虑重合数量和时间周期权重

因子值：综合多时间周期的接近程度和重合情况 (0-1)
信号强度：
- STRONG: 因子值>0.8且3个以上重合
- MEDIUM: 因子值>0.6且2个以上重合
- WEAK: 因子值>0.3
- NONE: 因子值≤0.3
"""
