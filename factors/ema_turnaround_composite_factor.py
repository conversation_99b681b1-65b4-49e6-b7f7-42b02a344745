#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
EMA拐头向上综合因子

结合斐波那契、EMA、价格震荡等多个维度的综合因子
这是通用版本的calculate_ema_turnaround_factors函数的面向对象实现
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any
from datetime import datetime

from .base_factor import BaseFactor, FactorResult
try:
    from indicators.talib_wrapper import calculate_ema_series
    from indicators.fibonacci_comprehensive import calculate_fibonacci_levels_enhanced
    from indicators.mean_ultra import detect_ema_turnaround
    from indicators.primary_signal_indicators import detect_price_oscillation
except ImportError:
    # 如果indicators模块不可用，提供占位函数
    def calculate_ema_series(*args, **kwargs):
        return {}
    def calculate_fibonacci_levels_enhanced(*args, **kwargs):
        return {}
    def detect_ema_turnaround(*args, **kwargs):
        return False
    def detect_price_oscillation(*args, **kwargs):
        return False


class EmaTurnaroundCompositeFactor(BaseFactor):
    """EMA拐头向上综合因子"""
    
    def __init__(self, 
                 ema_periods: List[int] = None,
                 oscillation_threshold: float = 0.02,
                 lookback_days: int = 5,
                 target_ema_period: int = 12):
        super().__init__("ema_turnaround_composite_factor", "composite")
        
        self.ema_periods = ema_periods or [12, 62, 144, 169, 377, 576, 676]
        self.oscillation_threshold = oscillation_threshold
        self.lookback_days = lookback_days
        self.target_ema_period = target_ema_period
        
        self.required_columns = ['high', 'low', 'close']
        self.min_data_length = max(self.ema_periods) if self.ema_periods else 676
    
    def calculate(self, data: pd.DataFrame, **kwargs) -> Optional[FactorResult]:
        """计算EMA拐头向上综合因子"""
        try:
            # 获取可选参数
            stock_code = kwargs.get('stock_code', '')
            start_low_price = kwargs.get('start_low_price', None)
            
            # 使用通用函数计算
            factors = calculate_ema_turnaround_factors(
                df=data,
                stock_code=stock_code,
                start_low_price=start_low_price,
                ema_periods=self.ema_periods,
                oscillation_threshold=self.oscillation_threshold,
                lookback_days=self.lookback_days
            )
            
            if not factors:
                return None
            
            # 提取主要因子值
            factor_value = factors.get('oscillation_turnaround_score', 0.0)
            signal_strength_level = factors.get('signal_strength_level', 'NONE')
            
            # 计算置信度
            confidence = factor_value
            if factors.get('ema12_turnaround_confirmed', False) and factors.get('price_near_key_level', False):
                confidence = min(confidence + 0.2, 1.0)  # 双重确认加分
            
            # 构建详细的元数据
            metadata = {
                'all_factors': factors,
                'oscillation_score': factors.get('oscillation_turnaround_score', 0.0),
                'ema12_turnaround_confirmed': factors.get('ema12_turnaround_confirmed', False),
                'ema12_turnaround_strength': factors.get('ema12_turnaround_strength', 0.0),
                'price_near_key_level': factors.get('price_near_key_level', False),
                'key_level_count': factors.get('key_level_count', 0),
                'best_oscillation_level': factors.get('best_oscillation_level', ''),
                'best_oscillation_value': factors.get('best_oscillation_value', 0.0),
                'best_oscillation_type': factors.get('best_oscillation_type', ''),
                'ema12_slope_recent': factors.get('ema12_slope_recent', 0.0),
                'ema12_consecutive_up_days': factors.get('ema12_consecutive_up_days', 0),
                'parameters': {
                    'ema_periods': self.ema_periods,
                    'oscillation_threshold': self.oscillation_threshold,
                    'lookback_days': self.lookback_days,
                    'target_ema_period': self.target_ema_period
                }
            }
            
            return FactorResult(
                factor_name=self.factor_name,
                factor_value=factor_value,
                factor_type=self.factor_type,
                confidence=confidence,
                signal_strength=signal_strength_level,
                metadata=metadata
            )
            
        except Exception as e:
            self.logger.error(f"计算EMA拐头向上综合因子失败: {e}")
            return None
    
    def get_factor_description(self) -> str:
        return f"""
EMA拐头向上综合因子

功能：检测股价在关键技术位附近震荡，然后EMA_12拐头向上的综合信号

检测维度：
1. 关键技术位震荡检测
   - EMA位：{self.ema_periods}
   - 斐波那契位：23.6%, 38.2%, 50%, 61.8%, 78.6%, 127.2%, 138.2%, 161.8%
   - 震荡阈值：{self.oscillation_threshold * 100:.1f}%

2. EMA_{self.target_ema_period}拐头向上检测
   - 回看天数：{self.lookback_days}
   - 斜率转正
   - 连续上升≥2天

3. 综合评分机制
   - 震荡强度权重：40%
   - 拐头强度权重：60%

因子值：综合震荡和拐头的评分 (0-1)
信号强度：
- STRONG: >0.7
- MEDIUM: 0.5-0.7
- WEAK: 0.3-0.5
- NONE: <0.3

适用场景：
- 趋势反转确认
- 关键位支撑买入
- 短期动量捕捉
"""


class EmaTurnaroundSignalDetector:
    """EMA拐头向上信号检测器（兼容原有接口）"""
    
    def __init__(self):
        self.factor = EmaTurnaroundCompositeFactor()
        self.logger = self.factor.logger
    
    def detect_signals(self, stock_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        检测EMA拐头向上信号（兼容intraday_stock_monitor接口）
        
        Args:
            stock_data: 包含股票代码、数据等信息的字典
            
        Returns:
            信号列表
        """
        try:
            stock_code = stock_data.get('stock_code', '')
            stock_name = stock_data.get('stock_name', stock_code)
            df = stock_data.get('dataframe', None)
            current_price = stock_data.get('current_price', None)
            
            if df is None or len(df) < 50:
                return []
            
            if current_price is None:
                current_price = float(df['close'].iloc[-1])
            
            # 计算因子
            result = self.factor.calculate_with_validation(
                df, 
                stock_code=stock_code,
                start_low_price=stock_data.get('start_low_price', None)
            )
            
            if not result or result.signal_strength == "NONE":
                return []
            
            # 构建信号
            signal = {
                'stock_code': stock_code,
                'stock_name': stock_name,
                'signal_type': 'ema_turnaround',
                'indicator_name': 'ema_turnaround_composite',
                'current_price': current_price,
                'indicator_value': result.metadata.get('best_oscillation_value', current_price),
                'deviation': abs(current_price - result.metadata.get('best_oscillation_value', current_price)) / current_price,
                'signal_time': datetime.now(),
                'factor_value': result.factor_value,
                'signal_strength': result.signal_strength,
                'confidence': result.confidence,
                'metadata': result.metadata
            }
            
            return [signal]
            
        except Exception as e:
            self.logger.error(f"检测EMA拐头向上信号失败 {stock_data.get('stock_code', '')}: {e}")
            return []
    
    def get_signal_description(self, signal: Dict[str, Any]) -> str:
        """获取信号描述"""
        metadata = signal.get('metadata', {})
        
        description_parts = [
            f"EMA拐头向上信号",
            f"综合评分: {signal.get('factor_value', 0):.3f}",
            f"信号强度: {signal.get('signal_strength', 'NONE')}",
            f"置信度: {signal.get('confidence', 0):.3f}"
        ]
        
        if metadata.get('best_oscillation_level'):
            description_parts.append(f"关键位: {metadata['best_oscillation_level']}")
        
        if metadata.get('ema12_turnaround_confirmed'):
            description_parts.append(f"EMA12拐头确认")
            description_parts.append(f"连续上升: {metadata.get('ema12_consecutive_up_days', 0)}天")
        
        return " | ".join(description_parts)


# 提供向后兼容的函数接口
def calculate_ema_turnaround_factor_oop(data: pd.DataFrame, **kwargs) -> Optional[FactorResult]:
    """
    面向对象版本的EMA拐头向上因子计算函数
    
    这是calculate_ema_turnaround_factors的面向对象封装版本
    """
    factor = EmaTurnaroundCompositeFactor()
    return factor.calculate_with_validation(data, **kwargs)


def detect_ema_turnaround_signals(stock_data: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    检测EMA拐头向上信号的便捷函数
    
    兼容intraday_stock_monitor的接口
    """
    detector = EmaTurnaroundSignalDetector()
    return detector.detect_signals(stock_data)
