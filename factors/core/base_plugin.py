#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
高级因子插件基类

支持Qlib兼容性、参数优化、训练和高性能向量化计算的插件基类
"""

import os
import sys
import numpy as np
import pandas as pd
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Union, Tuple
from dataclasses import dataclass, field
import yaml
import json
from pathlib import Path

from utils.logger import get_logger
from ..base_factor import BaseFactor, FactorResult

# 尝试导入Qlib相关模块
try:
    import qlib
    from qlib.data import D
    from qlib.model.base import Model
    QLIB_AVAILABLE = True
except ImportError:
    QLIB_AVAILABLE = False

# 尝试导入优化相关模块
try:
    from scipy.optimize import minimize, differential_evolution
    from sklearn.model_selection import ParameterGrid
    OPTIMIZATION_AVAILABLE = True
except ImportError:
    OPTIMIZATION_AVAILABLE = False


@dataclass
class FactorPluginInfo:
    """增强的因子插件信息"""
    name: str                           # 插件名称
    version: str                        # 版本号
    description: str                    # 描述
    author: str                         # 作者
    category: str                       # 分类 (technical/fundamental/sentiment/composite)
    dependencies: List[str]             # 数据依赖
    scenarios: List[str]                # 适用场景
    computational_cost: str             # 计算成本 (low/medium/high)
    priority: str                       # 优先级 (low/medium/high)
    tags: List[str]                     # 标签
    
    # 高级特性
    qlib_compatible: bool = False       # Qlib兼容性
    trainable: bool = False             # 是否可训练
    optimizable: bool = False           # 是否支持参数优化
    vectorized: bool = True             # 是否向量化实现
    
    # 性能指标
    min_data_length: int = 50           # 最小数据长度
    max_lookback: int = 252             # 最大回看期
    memory_usage: str = "low"           # 内存使用 (low/medium/high)
    
    # 配置相关
    config_schema: Dict[str, Any] = field(default_factory=dict)  # 配置模式
    default_config: Dict[str, Any] = field(default_factory=dict) # 默认配置
    
    enabled: bool = True                # 是否启用


class AdvancedFactorPlugin(ABC):
    """高级因子插件基类"""
    
    def __init__(self):
        self.logger = get_logger(f'Plugin_{self.get_plugin_info().name}')
        self._config_cache = {}
        self._optimization_history = []
        
    @classmethod
    @abstractmethod
    def get_plugin_info(cls) -> FactorPluginInfo:
        """获取插件信息"""
        pass
    
    @classmethod
    @abstractmethod
    def create_factor(cls, config: Dict[str, Any] = None) -> BaseFactor:
        """创建因子实例"""
        pass
    
    @classmethod
    def validate_config(cls, config: Dict[str, Any]) -> bool:
        """验证配置参数"""
        plugin_info = cls.get_plugin_info()
        schema = plugin_info.config_schema
        
        if not schema:
            return True
            
        try:
            # 基础类型验证
            for key, expected_type in schema.items():
                if key in config:
                    value = config[key]
                    if expected_type == 'int' and not isinstance(value, int):
                        return False
                    elif expected_type == 'float' and not isinstance(value, (int, float)):
                        return False
                    elif expected_type == 'list' and not isinstance(value, list):
                        return False
                    elif expected_type == 'bool' and not isinstance(value, bool):
                        return False
            
            return True
            
        except Exception:
            return False
    
    @classmethod
    def get_default_config(cls) -> Dict[str, Any]:
        """获取默认配置"""
        return cls.get_plugin_info().default_config.copy()
    
    @classmethod
    def get_config_schema(cls) -> Dict[str, Any]:
        """获取配置模式"""
        return cls.get_plugin_info().config_schema.copy()
    
    @classmethod
    def load_config_from_file(cls, config_path: str) -> Dict[str, Any]:
        """从文件加载配置"""
        try:
            config_file = Path(config_path)
            if not config_file.exists():
                return cls.get_default_config()
            
            if config_file.suffix.lower() == '.yaml' or config_file.suffix.lower() == '.yml':
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)
            elif config_file.suffix.lower() == '.json':
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
            else:
                raise ValueError(f"不支持的配置文件格式: {config_file.suffix}")
            
            # 合并默认配置
            default_config = cls.get_default_config()
            default_config.update(config)
            
            return default_config
            
        except Exception as e:
            cls._get_logger().error(f"加载配置文件失败 {config_path}: {e}")
            return cls.get_default_config()
    
    @classmethod
    def save_config_to_file(cls, config: Dict[str, Any], config_path: str):
        """保存配置到文件"""
        try:
            config_file = Path(config_path)
            config_file.parent.mkdir(parents=True, exist_ok=True)
            
            if config_file.suffix.lower() == '.yaml' or config_file.suffix.lower() == '.yml':
                with open(config_file, 'w', encoding='utf-8') as f:
                    yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
            elif config_file.suffix.lower() == '.json':
                with open(config_file, 'w', encoding='utf-8') as f:
                    json.dump(config, f, indent=2, ensure_ascii=False)
            else:
                raise ValueError(f"不支持的配置文件格式: {config_file.suffix}")
                
        except Exception as e:
            cls._get_logger().error(f"保存配置文件失败 {config_path}: {e}")
    
    @classmethod
    def optimize_parameters(cls, data: pd.DataFrame, 
                          param_ranges: Dict[str, Any],
                          objective_func: callable = None,
                          method: str = 'grid_search',
                          **kwargs) -> Dict[str, Any]:
        """参数优化"""
        if not OPTIMIZATION_AVAILABLE:
            cls._get_logger().warning("优化库不可用，返回默认配置")
            return cls.get_default_config()
        
        plugin_info = cls.get_plugin_info()
        if not plugin_info.optimizable:
            cls._get_logger().warning(f"插件 {plugin_info.name} 不支持参数优化")
            return cls.get_default_config()
        
        try:
            if method == 'grid_search':
                return cls._grid_search_optimization(data, param_ranges, objective_func, **kwargs)
            elif method == 'differential_evolution':
                return cls._differential_evolution_optimization(data, param_ranges, objective_func, **kwargs)
            else:
                raise ValueError(f"不支持的优化方法: {method}")
                
        except Exception as e:
            cls._get_logger().error(f"参数优化失败: {e}")
            return cls.get_default_config()
    
    @classmethod
    def _grid_search_optimization(cls, data: pd.DataFrame, 
                                param_ranges: Dict[str, Any],
                                objective_func: callable = None,
                                **kwargs) -> Dict[str, Any]:
        """网格搜索优化"""
        if objective_func is None:
            objective_func = cls._default_objective_function
        
        # 生成参数网格
        param_grid = list(ParameterGrid(param_ranges))
        best_score = float('-inf')
        best_params = cls.get_default_config()
        
        for params in param_grid:
            try:
                # 创建因子实例
                factor = cls.create_factor(params)
                
                # 计算目标函数值
                score = objective_func(factor, data, **kwargs)
                
                if score > best_score:
                    best_score = score
                    best_params = params.copy()
                    
            except Exception as e:
                cls._get_logger().debug(f"参数组合失败 {params}: {e}")
                continue
        
        cls._get_logger().info(f"网格搜索完成，最佳得分: {best_score:.4f}")
        return best_params
    
    @classmethod
    def _differential_evolution_optimization(cls, data: pd.DataFrame,
                                           param_ranges: Dict[str, Any],
                                           objective_func: callable = None,
                                           **kwargs) -> Dict[str, Any]:
        """差分进化优化"""
        if objective_func is None:
            objective_func = cls._default_objective_function
        
        # 构建边界
        bounds = []
        param_names = []
        
        for param_name, param_range in param_ranges.items():
            if isinstance(param_range, (list, tuple)) and len(param_range) == 2:
                bounds.append(param_range)
                param_names.append(param_name)
        
        if not bounds:
            cls._get_logger().warning("没有有效的参数范围，返回默认配置")
            return cls.get_default_config()
        
        def objective_wrapper(x):
            try:
                # 构建参数字典
                params = dict(zip(param_names, x))
                
                # 创建因子实例
                factor = cls.create_factor(params)
                
                # 计算目标函数值（取负值因为minimize函数）
                return -objective_func(factor, data, **kwargs)
                
            except Exception:
                return float('inf')  # 返回最差值
        
        # 执行优化
        result = differential_evolution(objective_wrapper, bounds, **kwargs)
        
        if result.success:
            best_params = dict(zip(param_names, result.x))
            cls._get_logger().info(f"差分进化优化完成，最佳得分: {-result.fun:.4f}")
            return best_params
        else:
            cls._get_logger().warning("差分进化优化失败，返回默认配置")
            return cls.get_default_config()
    
    @classmethod
    def _default_objective_function(cls, factor: BaseFactor, data: pd.DataFrame, **kwargs) -> float:
        """默认目标函数（可被子类重写）"""
        try:
            result = factor.calculate_with_validation(data, **kwargs)
            if result is None:
                return 0.0
            
            # 简单的目标函数：因子值 * 置信度
            return result.factor_value * result.confidence
            
        except Exception:
            return 0.0
    
    @classmethod
    def create_qlib_factor(cls, config: Dict[str, Any] = None):
        """创建Qlib兼容的因子（如果支持）"""
        plugin_info = cls.get_plugin_info()
        
        if not QLIB_AVAILABLE:
            raise ImportError("Qlib不可用")
        
        if not plugin_info.qlib_compatible:
            raise ValueError(f"插件 {plugin_info.name} 不支持Qlib")
        
        # 子类需要实现具体的Qlib因子创建逻辑
        raise NotImplementedError("子类需要实现create_qlib_factor方法")
    
    @classmethod
    def _get_logger(cls):
        """获取日志器"""
        return get_logger(f'Plugin_{cls.get_plugin_info().name}')


class VectorizedFactorMixin:
    """向量化计算混入类"""
    
    @staticmethod
    def vectorized_ema(data: np.ndarray, period: int) -> np.ndarray:
        """向量化EMA计算"""
        alpha = 2.0 / (period + 1)
        ema = np.zeros_like(data)
        ema[0] = data[0]
        
        for i in range(1, len(data)):
            ema[i] = alpha * data[i] + (1 - alpha) * ema[i-1]
        
        return ema
    
    @staticmethod
    def vectorized_sma(data: np.ndarray, period: int) -> np.ndarray:
        """向量化SMA计算"""
        return pd.Series(data).rolling(window=period).mean().values
    
    @staticmethod
    def vectorized_std(data: np.ndarray, period: int) -> np.ndarray:
        """向量化标准差计算"""
        return pd.Series(data).rolling(window=period).std().values
    
    @staticmethod
    def vectorized_bollinger_bands(data: np.ndarray, period: int, std_dev: float) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """向量化布林带计算"""
        sma = VectorizedFactorMixin.vectorized_sma(data, period)
        std = VectorizedFactorMixin.vectorized_std(data, period)
        
        upper = sma + std_dev * std
        lower = sma - std_dev * std
        
        return upper, sma, lower
    
    @staticmethod
    def vectorized_rsi(data: np.ndarray, period: int) -> np.ndarray:
        """向量化RSI计算"""
        delta = np.diff(data)
        gain = np.where(delta > 0, delta, 0)
        loss = np.where(delta < 0, -delta, 0)
        
        avg_gain = pd.Series(gain).rolling(window=period).mean().values
        avg_loss = pd.Series(loss).rolling(window=period).mean().values
        
        rs = avg_gain / (avg_loss + 1e-10)  # 避免除零
        rsi = 100 - (100 / (1 + rs))
        
        return np.concatenate([[np.nan], rsi])  # 添加第一个NaN值
