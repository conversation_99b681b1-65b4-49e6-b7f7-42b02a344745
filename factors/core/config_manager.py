#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
因子配置管理器

统一管理因子插件的配置，支持分层配置、环境配置和动态更新
"""

import os
import yaml
import json
from pathlib import Path
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass, field
import threading
from copy import deepcopy

from utils.logger import get_logger


@dataclass
class ConfigProfile:
    """配置档案"""
    name: str                           # 档案名称
    description: str                    # 描述
    scenario: str                       # 适用场景
    factors: Dict[str, Dict[str, Any]] = field(default_factory=dict)  # 因子配置
    global_settings: Dict[str, Any] = field(default_factory=dict)     # 全局设置


class FactorConfigManager:
    """因子配置管理器"""
    
    def __init__(self, config_dir: str = "config/factors"):
        self.logger = get_logger('FactorConfigManager')
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(parents=True, exist_ok=True)
        
        # 配置缓存
        self._config_cache: Dict[str, Dict[str, Any]] = {}
        self._profiles: Dict[str, ConfigProfile] = {}
        self._lock = threading.RLock()
        
        # 默认配置文件路径
        self.default_config_file = self.config_dir / "default.yaml"
        self.profiles_dir = self.config_dir / "profiles"
        self.profiles_dir.mkdir(exist_ok=True)
        
        # 初始化
        self._load_default_config()
        self._load_profiles()
        
        self.logger.info(f"因子配置管理器初始化完成，配置目录: {self.config_dir}")
    
    def _load_default_config(self):
        """加载默认配置"""
        try:
            if self.default_config_file.exists():
                with open(self.default_config_file, 'r', encoding='utf-8') as f:
                    default_config = yaml.safe_load(f) or {}
            else:
                default_config = self._create_default_config()
                self._save_default_config(default_config)
            
            with self._lock:
                self._config_cache['default'] = default_config
            
            self.logger.info("默认配置加载完成")
            
        except Exception as e:
            self.logger.error(f"加载默认配置失败: {e}")
            with self._lock:
                self._config_cache['default'] = self._create_default_config()
    
    def _create_default_config(self) -> Dict[str, Any]:
        """创建默认配置"""
        return {
            'global_settings': {
                'enable_caching': True,
                'cache_size': 1000,
                'enable_optimization': True,
                'enable_qlib': False,
                'default_scenario': 'intraday',
                'performance_monitoring': True
            },
            'factor_defaults': {
                'enabled': True,
                'priority': 'medium',
                'computational_cost': 'medium',
                'cache_results': True,
                'enable_validation': True
            },
            'scenarios': {
                'intraday': {
                    'description': '盘中实时监控',
                    'max_factors': 10,
                    'max_computational_cost': 'medium',
                    'enable_optimization': False
                },
                'after_market': {
                    'description': '盘后分析',
                    'max_factors': 20,
                    'max_computational_cost': 'high',
                    'enable_optimization': True
                },
                'research': {
                    'description': '研究分析',
                    'max_factors': -1,  # 无限制
                    'max_computational_cost': 'high',
                    'enable_optimization': True
                }
            }
        }
    
    def _save_default_config(self, config: Dict[str, Any]):
        """保存默认配置"""
        try:
            with open(self.default_config_file, 'w', encoding='utf-8') as f:
                yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
        except Exception as e:
            self.logger.error(f"保存默认配置失败: {e}")
    
    def _load_profiles(self):
        """加载配置档案"""
        try:
            for profile_file in self.profiles_dir.glob("*.yaml"):
                profile_name = profile_file.stem
                
                with open(profile_file, 'r', encoding='utf-8') as f:
                    profile_data = yaml.safe_load(f)
                
                if profile_data:
                    profile = ConfigProfile(
                        name=profile_name,
                        description=profile_data.get('description', ''),
                        scenario=profile_data.get('scenario', 'intraday'),
                        factors=profile_data.get('factors', {}),
                        global_settings=profile_data.get('global_settings', {})
                    )
                    
                    with self._lock:
                        self._profiles[profile_name] = profile
                    
                    self.logger.debug(f"加载配置档案: {profile_name}")
            
            self.logger.info(f"加载了 {len(self._profiles)} 个配置档案")
            
        except Exception as e:
            self.logger.error(f"加载配置档案失败: {e}")
    
    def get_factor_config(self, factor_name: str, 
                         profile: str = None, 
                         scenario: str = None) -> Dict[str, Any]:
        """获取因子配置"""
        with self._lock:
            # 获取基础配置
            base_config = self._config_cache.get('default', {})
            factor_defaults = base_config.get('factor_defaults', {})
            
            # 应用档案配置
            if profile and profile in self._profiles:
                profile_obj = self._profiles[profile]
                if factor_name in profile_obj.factors:
                    factor_config = deepcopy(factor_defaults)
                    factor_config.update(profile_obj.factors[factor_name])
                    return factor_config
            
            # 应用场景配置
            if scenario:
                scenario_config = base_config.get('scenarios', {}).get(scenario, {})
                factor_config = deepcopy(factor_defaults)
                
                # 场景特定的因子配置
                scenario_factors = scenario_config.get('factors', {})
                if factor_name in scenario_factors:
                    factor_config.update(scenario_factors[factor_name])
                
                return factor_config
            
            # 返回默认配置
            return deepcopy(factor_defaults)
    
    def get_scenario_config(self, scenario: str) -> Dict[str, Any]:
        """获取场景配置"""
        with self._lock:
            base_config = self._config_cache.get('default', {})
            scenarios = base_config.get('scenarios', {})
            return scenarios.get(scenario, {})
    
    def get_global_settings(self, profile: str = None) -> Dict[str, Any]:
        """获取全局设置"""
        with self._lock:
            base_settings = self._config_cache.get('default', {}).get('global_settings', {})
            
            if profile and profile in self._profiles:
                profile_obj = self._profiles[profile]
                settings = deepcopy(base_settings)
                settings.update(profile_obj.global_settings)
                return settings
            
            return deepcopy(base_settings)
    
    def create_profile(self, name: str, description: str, scenario: str,
                      factors: Dict[str, Dict[str, Any]] = None,
                      global_settings: Dict[str, Any] = None) -> bool:
        """创建配置档案"""
        try:
            profile = ConfigProfile(
                name=name,
                description=description,
                scenario=scenario,
                factors=factors or {},
                global_settings=global_settings or {}
            )
            
            # 保存到文件
            profile_file = self.profiles_dir / f"{name}.yaml"
            profile_data = {
                'description': description,
                'scenario': scenario,
                'factors': factors or {},
                'global_settings': global_settings or {}
            }
            
            with open(profile_file, 'w', encoding='utf-8') as f:
                yaml.dump(profile_data, f, default_flow_style=False, allow_unicode=True)
            
            # 更新缓存
            with self._lock:
                self._profiles[name] = profile
            
            self.logger.info(f"创建配置档案: {name}")
            return True
            
        except Exception as e:
            self.logger.error(f"创建配置档案失败 {name}: {e}")
            return False
    
    def update_factor_config(self, factor_name: str, config: Dict[str, Any],
                           profile: str = None, scenario: str = None) -> bool:
        """更新因子配置"""
        try:
            if profile:
                # 更新档案中的因子配置
                if profile not in self._profiles:
                    self.logger.error(f"配置档案不存在: {profile}")
                    return False
                
                with self._lock:
                    self._profiles[profile].factors[factor_name] = deepcopy(config)
                
                # 保存到文件
                self._save_profile(profile)
                
            elif scenario:
                # 更新场景中的因子配置
                with self._lock:
                    base_config = self._config_cache.get('default', {})
                    scenarios = base_config.setdefault('scenarios', {})
                    scenario_config = scenarios.setdefault(scenario, {})
                    scenario_factors = scenario_config.setdefault('factors', {})
                    scenario_factors[factor_name] = deepcopy(config)
                
                # 保存默认配置
                self._save_default_config(self._config_cache['default'])
            
            else:
                # 更新默认配置
                with self._lock:
                    base_config = self._config_cache.get('default', {})
                    factor_defaults = base_config.setdefault('factor_defaults', {})
                    factor_defaults.update(config)
                
                # 保存默认配置
                self._save_default_config(self._config_cache['default'])
            
            self.logger.info(f"更新因子配置: {factor_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"更新因子配置失败 {factor_name}: {e}")
            return False
    
    def _save_profile(self, profile_name: str):
        """保存配置档案"""
        try:
            if profile_name not in self._profiles:
                return
            
            profile = self._profiles[profile_name]
            profile_file = self.profiles_dir / f"{profile_name}.yaml"
            
            profile_data = {
                'description': profile.description,
                'scenario': profile.scenario,
                'factors': profile.factors,
                'global_settings': profile.global_settings
            }
            
            with open(profile_file, 'w', encoding='utf-8') as f:
                yaml.dump(profile_data, f, default_flow_style=False, allow_unicode=True)
                
        except Exception as e:
            self.logger.error(f"保存配置档案失败 {profile_name}: {e}")
    
    def list_profiles(self) -> List[str]:
        """列出所有配置档案"""
        with self._lock:
            return list(self._profiles.keys())
    
    def get_profile_info(self, profile_name: str) -> Optional[ConfigProfile]:
        """获取配置档案信息"""
        with self._lock:
            return self._profiles.get(profile_name)
    
    def delete_profile(self, profile_name: str) -> bool:
        """删除配置档案"""
        try:
            profile_file = self.profiles_dir / f"{profile_name}.yaml"
            if profile_file.exists():
                profile_file.unlink()
            
            with self._lock:
                if profile_name in self._profiles:
                    del self._profiles[profile_name]
            
            self.logger.info(f"删除配置档案: {profile_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"删除配置档案失败 {profile_name}: {e}")
            return False
    
    def reload_config(self):
        """重新加载配置"""
        try:
            with self._lock:
                self._config_cache.clear()
                self._profiles.clear()
            
            self._load_default_config()
            self._load_profiles()
            
            self.logger.info("配置重新加载完成")
            
        except Exception as e:
            self.logger.error(f"重新加载配置失败: {e}")
    
    def export_config(self, output_file: str, profile: str = None):
        """导出配置"""
        try:
            if profile:
                if profile not in self._profiles:
                    raise ValueError(f"配置档案不存在: {profile}")
                
                profile_obj = self._profiles[profile]
                export_data = {
                    'profile': {
                        'name': profile_obj.name,
                        'description': profile_obj.description,
                        'scenario': profile_obj.scenario,
                        'factors': profile_obj.factors,
                        'global_settings': profile_obj.global_settings
                    }
                }
            else:
                export_data = {
                    'default_config': self._config_cache.get('default', {}),
                    'profiles': {
                        name: {
                            'description': prof.description,
                            'scenario': prof.scenario,
                            'factors': prof.factors,
                            'global_settings': prof.global_settings
                        }
                        for name, prof in self._profiles.items()
                    }
                }
            
            output_path = Path(output_file)
            if output_path.suffix.lower() == '.json':
                with open(output_path, 'w', encoding='utf-8') as f:
                    json.dump(export_data, f, indent=2, ensure_ascii=False)
            else:
                with open(output_path, 'w', encoding='utf-8') as f:
                    yaml.dump(export_data, f, default_flow_style=False, allow_unicode=True)
            
            self.logger.info(f"配置导出完成: {output_file}")
            
        except Exception as e:
            self.logger.error(f"导出配置失败: {e}")


# 全局配置管理器实例
_config_manager = None

def get_config_manager() -> FactorConfigManager:
    """获取全局配置管理器实例"""
    global _config_manager
    if _config_manager is None:
        _config_manager = FactorConfigManager()
    return _config_manager
