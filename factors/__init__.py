# -*- coding: utf-8 -*-

"""
因子模块

提供统一的因子计算框架和通用函数
"""

# 基础框架
from .base_factor import BaseFactor, FactorResult, CompositeFactor, FactorManager

# 通用工具函数 - 从indicators模块导入
try:
    from indicators.fibonacci_comprehensive import calculate_fibonacci_levels_enhanced as calculate_fibonacci_levels
    from indicators.talib_wrapper import calculate_ema_series as calculate_ema_indicators
    from indicators.primary_signal_indicators import detect_price_oscillation
except ImportError:
    # 如果indicators模块不可用，提供占位函数
    def calculate_fibonacci_levels(*args, **kwargs):
        return {}
    def calculate_ema_indicators(*args, **kwargs):
        return {}
    def detect_price_oscillation(*args, **kwargs):
        return False

# 具体因子实现
from .fibonacci_factor import FibonacciFactor, MultiFibonacciFactor
from .ema_factor import EmaFactor, EmaTurnaroundFactor
from .bollinger_factor import BollingerFactor, BollingerConfig, BollingerSqueezeReleaseFactor
from .ema_turnaround_composite_factor import (
    EmaTurnaroundCompositeFactor,
    EmaTurnaroundSignalDetector,
    calculate_ema_turnaround_factor_oop,
    detect_ema_turnaround_signals
)
# 背离因子模块（统一架构）
from .divergence import (
    BaseDivergenceFactor, DivergenceConfig,
    MACDDivergenceFactor, MACDDivergenceConfig,
    KDJDivergenceFactor, KDJDivergenceConfig,
    RSIDivergenceFactor, RSIDivergenceConfig,
    create_divergence_factor, get_all_divergence_factors, compare_divergence_signals
)

# 向后兼容：保留原有MACD因子的导入路径
try:
    from .macd_divergence_factor import MACDDivergenceFactor as LegacyMACDDivergenceFactor
except ImportError:
    LegacyMACDDivergenceFactor = MACDDivergenceFactor

__all__ = [
    # 基础框架
    'BaseFactor',
    'FactorResult',
    'CompositeFactor',
    'FactorManager',

    # 通用工具函数
    'calculate_fibonacci_levels',
    'calculate_ema_indicators',
    'detect_price_oscillation',

    # 具体因子
    'FibonacciFactor',
    'MultiFibonacciFactor',
    'EmaFactor',
    'EmaTurnaroundFactor',
    'BollingerFactor',
    'BollingerConfig',
    'BollingerSqueezeReleaseFactor',
    'EmaTurnaroundCompositeFactor',
    'EmaTurnaroundSignalDetector',
    # 背离因子模块（统一架构）
    'BaseDivergenceFactor',
    'DivergenceConfig',
    'MACDDivergenceFactor',
    'MACDDivergenceConfig',
    'KDJDivergenceFactor',
    'KDJDivergenceConfig',
    'RSIDivergenceFactor',
    'RSIDivergenceConfig',
    'create_divergence_factor',
    'get_all_divergence_factors',
    'compare_divergence_signals',

    # 向后兼容
    'LegacyMACDDivergenceFactor',

    # 便捷函数
    'calculate_ema_turnaround_factor_oop',
    'detect_ema_turnaround_signals'
]
