#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
成交量类因子插件

包含成交量激增、成交量分布、价量关系等成交量相关的因子插件
"""

import numpy as np
import pandas as pd
from typing import Dict, Any, Optional, List, Tuple
import talib

from ..core.base_plugin import AdvancedFactorPlugin, FactorPluginInfo, VectorizedFactorMixin
from ..base_factor import BaseFactor, FactorResult


class VolumeSpikeFactorPlugin(AdvancedFactorPlugin, VectorizedFactorMixin):
    """成交量激增因子插件"""
    
    @classmethod
    def get_plugin_info(cls) -> FactorPluginInfo:
        return FactorPluginInfo(
            name="volume_spike_factor",
            version="2.0.0",
            description="成交量激增因子，检测异常的成交量放大，支持多种检测算法",
            author="QuantFM Team",
            category="volume",
            dependencies=["volume"],
            scenarios=["intraday", "swing"],
            computational_cost="low",
            priority="high",
            tags=["volume", "anomaly", "spike"],
            
            # 高级特性
            qlib_compatible=True,
            trainable=True,
            optimizable=True,
            vectorized=True,
            
            # 性能指标
            min_data_length=50,
            max_lookback=100,
            memory_usage="low",
            
            # 配置模式
            config_schema={
                'lookback_period': 'int',
                'spike_threshold': 'float',
                'method': 'str',
                'percentile_threshold': 'float'
            },
            default_config={
                'lookback_period': 20,
                'spike_threshold': 2.0,
                'method': 'mean',  # mean, median, percentile
                'percentile_threshold': 0.9
            }
        )
    
    @classmethod
    def create_factor(cls, config: Dict[str, Any] = None) -> BaseFactor:
        config = config or cls.get_default_config()
        return VolumeSpikeFactor(
            lookback_period=config.get('lookback_period', 20),
            spike_threshold=config.get('spike_threshold', 2.0),
            method=config.get('method', 'mean'),
            percentile_threshold=config.get('percentile_threshold', 0.9)
        )


class VolumeSpikeFactor(BaseFactor, VectorizedFactorMixin):
    """成交量激增因子实现"""
    
    def __init__(self, lookback_period: int = 20, spike_threshold: float = 2.0,
                 method: str = 'mean', percentile_threshold: float = 0.9):
        super().__init__("volume_spike_factor", "volume")
        self.lookback_period = lookback_period
        self.spike_threshold = spike_threshold
        self.method = method
        self.percentile_threshold = percentile_threshold
        
        self.min_data_length = lookback_period + 10
        self.required_columns = ['volume']
    
    def calculate(self, data: pd.DataFrame, **kwargs) -> Optional[FactorResult]:
        """计算成交量激增因子"""
        try:
            volume_data = data['volume'].values
            current_volume = volume_data[-1]
            
            # 获取历史成交量
            historical_volume = volume_data[-self.lookback_period-1:-1]
            
            if len(historical_volume) < self.lookback_period or current_volume <= 0:
                return None
            
            # 根据方法计算基准值
            if self.method == 'mean':
                baseline = np.mean(historical_volume)
            elif self.method == 'median':
                baseline = np.median(historical_volume)
            elif self.method == 'percentile':
                baseline = np.percentile(historical_volume, self.percentile_threshold * 100)
            else:
                baseline = np.mean(historical_volume)
            
            if baseline <= 0:
                return None
            
            # 计算成交量比率
            volume_ratio = current_volume / baseline
            
            # 计算因子值
            if volume_ratio >= self.spike_threshold:
                # 使用对数缩放避免极值
                factor_value = min(1.0, np.log(volume_ratio / self.spike_threshold + 1) / np.log(10))
            else:
                factor_value = 0.0
            
            # 计算置信度（基于历史波动性）
            volume_cv = np.std(historical_volume) / np.mean(historical_volume)
            confidence = min(1.0, factor_value * (1 + 1 / (volume_cv + 1)))
            
            # 确定信号强度
            if volume_ratio >= self.spike_threshold * 5:
                signal_strength = "STRONG"
            elif volume_ratio >= self.spike_threshold * 3:
                signal_strength = "MEDIUM"
            elif volume_ratio >= self.spike_threshold:
                signal_strength = "WEAK"
            else:
                signal_strength = "NONE"
            
            return FactorResult(
                factor_name=self.factor_name,
                factor_value=factor_value,
                factor_type=self.factor_type,
                confidence=confidence,
                signal_strength=signal_strength,
                metadata={
                    'current_volume': current_volume,
                    'baseline_volume': baseline,
                    'volume_ratio': volume_ratio,
                    'spike_threshold': self.spike_threshold,
                    'method': self.method,
                    'volume_cv': volume_cv
                }
            )
            
        except Exception as e:
            self.logger.error(f"成交量激增因子计算失败: {e}")
            return None


class PriceVolumeFactorPlugin(AdvancedFactorPlugin, VectorizedFactorMixin):
    """价量关系因子插件"""
    
    @classmethod
    def get_plugin_info(cls) -> FactorPluginInfo:
        return FactorPluginInfo(
            name="price_volume_factor",
            version="2.0.0",
            description="价量关系因子，分析价格变动与成交量的关系",
            author="QuantFM Team",
            category="volume",
            dependencies=["close", "volume"],
            scenarios=["intraday", "swing", "after_market"],
            computational_cost="medium",
            priority="medium",
            tags=["volume", "price_action", "correlation"],
            
            # 高级特性
            qlib_compatible=True,
            trainable=True,
            optimizable=True,
            vectorized=True,
            
            # 性能指标
            min_data_length=50,
            max_lookback=100,
            memory_usage="medium",
            
            # 配置模式
            config_schema={
                'lookback_period': 'int',
                'correlation_threshold': 'float',
                'divergence_threshold': 'float'
            },
            default_config={
                'lookback_period': 20,
                'correlation_threshold': 0.3,
                'divergence_threshold': 0.5
            }
        )
    
    @classmethod
    def create_factor(cls, config: Dict[str, Any] = None) -> BaseFactor:
        config = config or cls.get_default_config()
        return PriceVolumeFactor(
            lookback_period=config.get('lookback_period', 20),
            correlation_threshold=config.get('correlation_threshold', 0.3),
            divergence_threshold=config.get('divergence_threshold', 0.5)
        )


class PriceVolumeFactor(BaseFactor, VectorizedFactorMixin):
    """价量关系因子实现"""
    
    def __init__(self, lookback_period: int = 20, correlation_threshold: float = 0.3,
                 divergence_threshold: float = 0.5):
        super().__init__("price_volume_factor", "volume")
        self.lookback_period = lookback_period
        self.correlation_threshold = correlation_threshold
        self.divergence_threshold = divergence_threshold
        
        self.min_data_length = lookback_period + 10
        self.required_columns = ['close', 'volume']
    
    def calculate(self, data: pd.DataFrame, **kwargs) -> Optional[FactorResult]:
        """计算价量关系因子"""
        try:
            # 获取最近的数据
            recent_data = data.tail(self.lookback_period)
            
            if len(recent_data) < self.lookback_period:
                return None
            
            # 计算价格变化率和成交量变化率
            price_changes = recent_data['close'].pct_change().dropna()
            volume_changes = recent_data['volume'].pct_change().dropna()
            
            if len(price_changes) < 10 or len(volume_changes) < 10:
                return None
            
            # 计算价量相关性
            correlation = np.corrcoef(price_changes, volume_changes)[0, 1]
            if np.isnan(correlation):
                correlation = 0.0
            
            # 检测价量背离
            divergence_signal = self._detect_price_volume_divergence(
                recent_data['close'], recent_data['volume']
            )
            
            # 计算成交量确认信号
            confirmation_signal = self._calculate_volume_confirmation(
                price_changes, volume_changes
            )
            
            # 计算因子值
            factor_value = 0.0
            
            # 价量协同信号
            if abs(correlation) > self.correlation_threshold:
                if correlation > 0:
                    factor_value += 0.4  # 正相关
                else:
                    factor_value += 0.2  # 负相关（可能是反转信号）
            
            # 背离信号
            if divergence_signal == 'bullish_divergence':
                factor_value += 0.5
            elif divergence_signal == 'bearish_divergence':
                factor_value += 0.3
            
            # 成交量确认信号
            factor_value += confirmation_signal * 0.3
            
            factor_value = min(1.0, factor_value)
            
            # 计算置信度
            confidence = min(1.0, abs(correlation) + 0.3)
            
            # 确定信号强度
            if factor_value > 0.7:
                signal_strength = "STRONG"
            elif factor_value > 0.5:
                signal_strength = "MEDIUM"
            elif factor_value > 0.3:
                signal_strength = "WEAK"
            else:
                signal_strength = "NONE"
            
            return FactorResult(
                factor_name=self.factor_name,
                factor_value=factor_value,
                factor_type=self.factor_type,
                confidence=confidence,
                signal_strength=signal_strength,
                metadata={
                    'correlation': correlation,
                    'divergence_signal': divergence_signal,
                    'confirmation_signal': confirmation_signal,
                    'price_changes_mean': np.mean(price_changes),
                    'volume_changes_mean': np.mean(volume_changes)
                }
            )
            
        except Exception as e:
            self.logger.error(f"价量关系因子计算失败: {e}")
            return None
    
    def _detect_price_volume_divergence(self, prices: pd.Series, volumes: pd.Series) -> str:
        """检测价量背离"""
        try:
            if len(prices) < 10 or len(volumes) < 10:
                return 'none'
            
            # 计算价格和成交量的趋势
            price_trend = np.polyfit(range(len(prices)), prices.values, 1)[0]
            volume_trend = np.polyfit(range(len(volumes)), volumes.values, 1)[0]
            
            # 标准化趋势值
            price_trend_norm = price_trend / prices.mean()
            volume_trend_norm = volume_trend / volumes.mean()
            
            # 检测背离
            if (price_trend_norm > self.divergence_threshold and 
                volume_trend_norm < -self.divergence_threshold):
                return 'bearish_divergence'  # 价格上涨但成交量下降
            elif (price_trend_norm < -self.divergence_threshold and 
                  volume_trend_norm > self.divergence_threshold):
                return 'bullish_divergence'  # 价格下跌但成交量上升
            
            return 'none'
            
        except Exception:
            return 'none'
    
    def _calculate_volume_confirmation(self, price_changes: pd.Series, 
                                     volume_changes: pd.Series) -> float:
        """计算成交量确认信号"""
        try:
            # 计算上涨日和下跌日的成交量表现
            up_days = price_changes > 0
            down_days = price_changes < 0
            
            if not up_days.any() or not down_days.any():
                return 0.0
            
            # 上涨日的平均成交量变化
            up_volume_change = volume_changes[up_days].mean()
            # 下跌日的平均成交量变化
            down_volume_change = volume_changes[down_days].mean()
            
            # 理想情况：上涨时成交量放大，下跌时成交量缩小
            if up_volume_change > 0 and down_volume_change < 0:
                return min(1.0, (up_volume_change - down_volume_change) / 2)
            elif up_volume_change > down_volume_change:
                return min(1.0, (up_volume_change - down_volume_change) / 4)
            else:
                return 0.0
                
        except Exception:
            return 0.0
