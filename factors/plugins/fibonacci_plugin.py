#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
斐波那契因子插件

将斐波那契因子转换为插件形式
"""

from typing import Dict, Any, Optional
import pandas as pd

from ..plugin_system import FactorPlugin, FactorPluginInfo
from ..base_factor import BaseFactor, FactorResult
from .. import calculate_fibonacci_levels


class FibonacciFactorPlugin(FactorPlugin):
    """斐波那契因子插件"""
    
    @classmethod
    def get_plugin_info(cls) -> FactorPluginInfo:
        return FactorPluginInfo(
            name="fibonacci_factor",
            version="1.0.0",
            description="斐波那契回撤位因子，检测价格是否接近关键斐波那契位",
            author="QuantFM Team",
            dependencies=["close", "high", "low"],
            scenarios=["intraday", "swing", "position"],
            computational_cost="medium",
            priority="high",
            tags=["technical", "support_resistance", "fibonacci"],
            enabled=True
        )
    
    @classmethod
    def create_factor(cls, config: Dict[str, Any] = None) -> BaseFactor:
        config = config or cls.get_default_config()
        return FibonacciFactor(
            lookback_period=config.get('lookback_period', 252),
            price_threshold=config.get('price_threshold', 0.005)
        )
    
    @classmethod
    def validate_config(cls, config: Dict[str, Any]) -> bool:
        """验证配置参数"""
        if 'lookback_period' in config:
            if not isinstance(config['lookback_period'], int) or config['lookback_period'] <= 0:
                return False
        
        if 'price_threshold' in config:
            if not isinstance(config['price_threshold'], (int, float)) or config['price_threshold'] <= 0:
                return False
        
        return True
    
    @classmethod
    def get_default_config(cls) -> Dict[str, Any]:
        return {
            'lookback_period': 252,
            'price_threshold': 0.005
        }


class FibonacciFactor(BaseFactor):
    """斐波那契因子实现"""
    
    def __init__(self, lookback_period: int = 252, price_threshold: float = 0.005):
        super().__init__("fibonacci_factor", "technical")
        self.lookback_period = lookback_period
        self.price_threshold = price_threshold
        self.min_data_length = max(50, lookback_period)
        self.required_columns = ['close', 'high', 'low']
    
    def calculate(self, data: pd.DataFrame, **kwargs) -> Optional[FactorResult]:
        """计算斐波那契因子"""
        try:
            current_price = float(data['close'].iloc[-1])
            
            # 计算斐波那契位
            fib_levels = calculate_fibonacci_levels(data, self.lookback_period)
            
            if not fib_levels:
                return None
            
            # 检测价格是否接近任何斐波那契位
            near_levels = []
            
            for fib_name, fib_value in fib_levels.items():
                if fib_value > 0:
                    deviation = abs(current_price - fib_value) / fib_value
                    if deviation <= self.price_threshold:
                        near_levels.append({
                            'level': fib_name,
                            'value': fib_value,
                            'deviation': deviation,
                            'direction': 'above' if current_price > fib_value else 'below'
                        })
            
            if not near_levels:
                return FactorResult(
                    factor_name=self.factor_name,
                    factor_value=0.0,
                    factor_type=self.factor_type,
                    confidence=0.0,
                    signal_strength="NONE",
                    metadata={'fibonacci_levels': fib_levels, 'current_price': current_price}
                )
            
            # 计算因子值（基于最接近的斐波那契位）
            closest_level = min(near_levels, key=lambda x: x['deviation'])
            factor_value = 1.0 - closest_level['deviation'] / self.price_threshold
            
            # 计算置信度
            confidence = min(1.0, factor_value)
            
            # 确定信号强度
            if factor_value > 0.8:
                signal_strength = "STRONG"
            elif factor_value > 0.6:
                signal_strength = "MEDIUM"
            elif factor_value > 0.3:
                signal_strength = "WEAK"
            else:
                signal_strength = "NONE"
            
            return FactorResult(
                factor_name=self.factor_name,
                factor_value=factor_value,
                factor_type=self.factor_type,
                confidence=confidence,
                signal_strength=signal_strength,
                metadata={
                    'fibonacci_levels': fib_levels,
                    'near_levels': near_levels,
                    'closest_level': closest_level,
                    'current_price': current_price
                }
            )
            
        except Exception as e:
            self.logger.error(f"斐波那契因子计算失败: {e}")
            return None


class MultiFibonacciFactorPlugin(FactorPlugin):
    """多时间周期斐波那契因子插件"""
    
    @classmethod
    def get_plugin_info(cls) -> FactorPluginInfo:
        return FactorPluginInfo(
            name="multi_fibonacci_factor",
            version="1.0.0",
            description="多时间周期斐波那契因子，综合多个时间周期的斐波那契位",
            author="QuantFM Team",
            dependencies=["close", "high", "low"],
            scenarios=["swing", "position"],  # 不适用于高频盘中监控
            computational_cost="high",
            priority="medium",
            tags=["technical", "multi_timeframe", "fibonacci"],
            enabled=True
        )
    
    @classmethod
    def create_factor(cls, config: Dict[str, Any] = None) -> BaseFactor:
        config = config or cls.get_default_config()
        return MultiFibonacciFactor(
            periods=config.get('periods', [63, 126, 252]),
            price_threshold=config.get('price_threshold', 0.005)
        )
    
    @classmethod
    def get_default_config(cls) -> Dict[str, Any]:
        return {
            'periods': [63, 126, 252],
            'price_threshold': 0.005
        }


class MultiFibonacciFactor(BaseFactor):
    """多时间周期斐波那契因子实现"""
    
    def __init__(self, periods: list = None, price_threshold: float = 0.005):
        super().__init__("multi_fibonacci_factor", "technical")
        self.periods = periods or [63, 126, 252]
        self.price_threshold = price_threshold
        self.min_data_length = max(self.periods) + 50
        self.required_columns = ['close', 'high', 'low']
    
    def calculate(self, data: pd.DataFrame, **kwargs) -> Optional[FactorResult]:
        """计算多时间周期斐波那契因子"""
        try:
            current_price = float(data['close'].iloc[-1])
            all_near_levels = []
            all_fib_levels = {}
            
            # 计算每个时间周期的斐波那契位
            for period in self.periods:
                if len(data) < period + 50:
                    continue
                
                fib_levels = calculate_fibonacci_levels(data, period)
                if not fib_levels:
                    continue
                
                all_fib_levels[f'period_{period}'] = fib_levels
                
                # 检测接近的斐波那契位
                for fib_name, fib_value in fib_levels.items():
                    if fib_value > 0:
                        deviation = abs(current_price - fib_value) / fib_value
                        if deviation <= self.price_threshold:
                            all_near_levels.append({
                                'period': period,
                                'level': fib_name,
                                'value': fib_value,
                                'deviation': deviation,
                                'weight': 1.0 / len(self.periods)
                            })
            
            if not all_near_levels:
                return FactorResult(
                    factor_name=self.factor_name,
                    factor_value=0.0,
                    factor_type=self.factor_type,
                    confidence=0.0,
                    signal_strength="NONE",
                    metadata={'all_fibonacci_levels': all_fib_levels, 'current_price': current_price}
                )
            
            # 计算加权因子值
            weighted_value = sum(
                (1.0 - level['deviation'] / self.price_threshold) * level['weight']
                for level in all_near_levels
            )
            
            factor_value = min(1.0, weighted_value)
            confidence = factor_value * (len(all_near_levels) / len(self.periods))
            
            # 确定信号强度
            if factor_value > 0.8 and len(all_near_levels) >= 2:
                signal_strength = "STRONG"
            elif factor_value > 0.6:
                signal_strength = "MEDIUM"
            elif factor_value > 0.3:
                signal_strength = "WEAK"
            else:
                signal_strength = "NONE"
            
            return FactorResult(
                factor_name=self.factor_name,
                factor_value=factor_value,
                factor_type=self.factor_type,
                confidence=confidence,
                signal_strength=signal_strength,
                metadata={
                    'all_fibonacci_levels': all_fib_levels,
                    'near_levels': all_near_levels,
                    'periods_analyzed': len(all_fib_levels),
                    'current_price': current_price
                }
            )
            
        except Exception as e:
            self.logger.error(f"多时间周期斐波那契因子计算失败: {e}")
            return None
