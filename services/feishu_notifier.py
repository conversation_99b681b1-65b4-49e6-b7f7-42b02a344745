#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
飞书通知器

实现成交量异动信号的飞书富媒体通知。

作者: QuantFM Team
创建时间: 2025-07-13
"""

import requests
import json
import hashlib
import hmac
import base64
import time
from datetime import datetime
from typing import List, Dict, Any
import sys
import os

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


class FeishuNotifier:
    """飞书通知器 - 基于官方API文档实现"""

    def __init__(self, webhook_url: str, secret: str, logger, use_signature: bool = True):
        """
        初始化飞书通知器

        Args:
            webhook_url: 飞书webhook地址
            secret: 飞书机器人密钥
            logger: 日志记录器
            use_signature: 是否使用签名验证（默认True）
        """
        self.webhook_url = webhook_url
        self.secret = secret
        self.logger = logger
        self.use_signature = use_signature  # 新增：控制是否使用签名

        # 通知频率控制
        self.last_notify_time = {}  # {stock_code: timestamp}
        self.notify_interval = 300  # 同一股票5分钟内只通知一次
        self.max_notifications_per_minute = 20  # 每分钟最大通知数
        self.notification_count = 0
        self.last_minute_reset = time.time()

        # 启用状态控制 - 修复缺失的enabled属性
        # 如果不使用签名，只需要webhook_url即可
        if self.use_signature:
            self.enabled = bool(self.webhook_url and self.secret)
        else:
            self.enabled = bool(self.webhook_url)

        # 验证配置
        if not self.webhook_url:
            self.logger.warning("飞书webhook URL未配置，通知功能已禁用")
        elif self.use_signature and not self.secret:
            self.logger.warning("飞书secret未配置但启用了签名验证，通知功能已禁用")
        else:
            signature_status = "启用签名验证" if self.use_signature else "禁用签名验证"
            self.logger.info(f"飞书通知器初始化完成 - {signature_status}")
    
    def send_strategy_signals(self, signals: List) -> bool:
        """
        发送策略信号到飞书

        Args:
            signals: 策略信号列表

        Returns:
            是否发送成功
        """
        if not self.webhook_url or not self.secret:
            self.logger.warning("飞书配置不完整，跳过通知")
            return False

        if not signals:
            return True

        try:
            # 构建汇总消息
            message = self._build_strategy_summary_message(signals)

            # 发送消息
            success = self._send_message(message)

            if success:
                self.logger.info(f"成功发送策略信号汇总到飞书，包含 {len(signals)} 个信号")

            return success

        except Exception as e:
            self.logger.error(f"发送策略信号到飞书失败: {e}")
            return False

    def send_signals(self, signals: List) -> bool:
        """
        发送信号到飞书
        
        Args:
            signals: 异动信号列表
            
        Returns:
            是否发送成功
        """
        if not self.webhook_url or not self.secret:
            self.logger.warning("飞书配置不完整，跳过通知")
            return False
        
        success_count = 0
        
        for signal in signals:
            try:
                # 频率控制检查
                if self._should_skip_notification(signal.stock_code):
                    continue
                
                # 每分钟通知数量控制
                if not self._check_rate_limit():
                    self.logger.warning("达到每分钟通知数量限制，跳过后续通知")
                    break
                
                # 发送单个信号
                if self._send_single_signal(signal):
                    success_count += 1
                    # 更新通知时间
                    self.last_notify_time[signal.stock_code] = time.time()
                    self.notification_count += 1
                
            except Exception as e:
                self.logger.error(f"发送信号 {signal.stock_code} 到飞书失败: {e}")
        
        if success_count > 0:
            self.logger.info(f"成功发送 {success_count}/{len(signals)} 个信号到飞书")
        
        return success_count > 0
    
    def _should_skip_notification(self, stock_code: str) -> bool:
        """检查是否应该跳过通知"""
        last_time = self.last_notify_time.get(stock_code, 0)
        return (time.time() - last_time) < self.notify_interval
    
    def _check_rate_limit(self) -> bool:
        """检查速率限制"""
        current_time = time.time()
        
        # 重置计数器（每分钟）
        if current_time - self.last_minute_reset > 60:
            self.notification_count = 0
            self.last_minute_reset = current_time
        
        return self.notification_count < self.max_notifications_per_minute
    
    def _send_message(self, message: Dict) -> bool:
        """
        发送消息到飞书 - 支持有签名和无签名两种模式

        Args:
            message: 消息内容（卡片格式）

        Returns:
            是否发送成功
        """
        try:
            # 构建请求数据
            if self.use_signature:
                # 有签名模式 - 使用秒级时间戳
                timestamp = str(int(time.time()))
                sign = self._generate_sign(timestamp)

                data = {
                    "timestamp": timestamp,
                    "sign": sign,
                    "msg_type": "interactive",
                    "card": message
                }

                self.logger.info(f"发送飞书消息（有签名模式）:")
                self.logger.info(f"  timestamp: {timestamp}")
                self.logger.info(f"  sign: {sign[:20]}...")
            else:
                # 无签名模式 - 直接发送消息内容
                data = {
                    "msg_type": "interactive",
                    "card": message
                }

                self.logger.info(f"发送飞书消息（无签名模式）")

            # 发送请求 - 绕过代理直接访问
            response = requests.post(
                self.webhook_url,
                json=data,
                headers={'Content-Type': 'application/json'},
                timeout=10,
                proxies={'http': '', 'https': ''}  # 禁用代理
            )

            if response.status_code == 200:
                response_data = response.json()
                if response_data.get("code") == 0:
                    self.logger.debug("成功发送消息到飞书")
                    return True
                else:
                    self.logger.error(f"飞书API返回错误: {response_data}")
                    return False
            else:
                self.logger.error(f"发送消息到飞书失败: {response.status_code} - {response.text}")
                return False

        except Exception as e:
            self.logger.error(f"发送消息失败: {e}")
            return False

    def _send_text_message(self, text: str) -> bool:
        """
        发送文本消息到飞书 - 完全按照官方文档实现

        官方文档要求的文本消息格式：
        {
            "timestamp": "1599360473",
            "sign": "signature",
            "msg_type": "text",
            "content": {
                "text": "消息内容"
            }
        }
        """
        try:
            # 构建请求数据 - 支持有签名和无签名两种模式
            if self.use_signature:
                # 有签名模式 - 使用秒级时间戳
                timestamp = str(int(time.time()))
                sign = self._generate_sign(timestamp)

                if not sign:
                    self.logger.error("签名生成失败，无法发送消息")
                    return False

                data = {
                    "timestamp": timestamp,
                    "sign": sign,
                    "msg_type": "text",
                    "content": {
                        "text": text
                    }
                }

                self.logger.info(f"发送飞书文本消息（有签名模式）:")
                self.logger.info(f"  timestamp: {timestamp}")
                self.logger.info(f"  sign: {sign[:20]}...")
            else:
                # 无签名模式 - 直接发送文本内容
                data = {
                    "msg_type": "text",
                    "content": {
                        "text": text
                    }
                }

                self.logger.info(f"发送飞书文本消息（无签名模式）:")

            self.logger.info(f"  text: {text}")

            # 发送请求 - 使用官方推荐的请求头
            headers = {
                "Content-Type": "application/json; charset=utf-8"
            }

            response = requests.post(
                self.webhook_url,
                json=data,
                headers=headers,
                timeout=10,
                proxies={'http': '', 'https': ''}  # 禁用代理，直接访问
            )

            # 处理响应
            self.logger.info(f"飞书响应状态码: {response.status_code}")
            self.logger.info(f"飞书响应内容: {response.text}")

            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 0:
                    self.logger.info("✅ 飞书文本消息发送成功")
                    return True
                else:
                    self.logger.error(f"❌ 飞书API返回错误: {result}")
                    # 特殊处理签名错误
                    if result.get("code") == 19021:
                        self.logger.error("签名验证失败，请检查secret和时间戳")
                        self.logger.error(f"当前时间戳: {timestamp}")
                        self.logger.error(f"当前签名: {sign}")
                    return False
            else:
                self.logger.error(f"❌ 飞书HTTP请求失败: {response.status_code}, {response.text}")
                return False

        except Exception as e:
            self.logger.error(f"❌ 发送飞书文本消息异常: {e}")
            import traceback
            self.logger.error(f"异常详情: {traceback.format_exc()}")
            return False

    def _get_corrected_timestamp(self) -> str:
        """
        获取适合飞书的时间戳

        尝试不同的时间戳策略来解决时间验证问题
        """
        try:
            import time
            from datetime import datetime, timezone

            # 当前时间是2025年8月19日，系统时间是正确的
            # 问题可能在于时区处理

            # 策略1: 使用当前本地时间戳
            local_timestamp = int(time.time())

            # 策略2: 使用UTC时间戳
            utc_now = datetime.now(timezone.utc)
            utc_timestamp = int(utc_now.timestamp())

            # 策略3: 手动调整时区偏移
            # 中国是UTC+8，如果飞书期望UTC时间，需要减去8小时
            timezone_offset = 8 * 3600  # 8小时的秒数
            adjusted_timestamp = local_timestamp - timezone_offset

            self.logger.info(f"时间戳策略分析:")
            self.logger.info(f"  本地时间戳: {local_timestamp} -> {datetime.fromtimestamp(local_timestamp)}")
            self.logger.info(f"  UTC时间戳: {utc_timestamp} -> {datetime.fromtimestamp(utc_timestamp, tz=timezone.utc)}")
            self.logger.info(f"  时区调整戳: {adjusted_timestamp} -> {datetime.fromtimestamp(adjusted_timestamp)}")

            # 尝试使用时区调整后的时间戳
            return str(adjusted_timestamp)

        except Exception as e:
            self.logger.error(f"时间戳生成失败: {e}")
            # 如果失败，返回原始时间戳
            return str(int(time.time()))

    def _send_single_signal(self, signal) -> bool:
        """发送单个信号"""
        try:
            # 构建消息内容
            message = self._build_message(signal)
            return self._send_message(message)

        except Exception as e:
            self.logger.error(f"发送单个信号失败: {e}")
            return False

    def _build_strategy_summary_message(self, signals: List) -> Dict:
        """构建策略信号汇总消息"""
        try:
            # 统计信号
            strategy_stats = {}
            top_signals = []

            for signal in signals:
                strategy_name = signal.strategy_name
                if strategy_name not in strategy_stats:
                    strategy_stats[strategy_name] = 0
                strategy_stats[strategy_name] += 1

                # 收集高强度信号
                if signal.signal_strength >= 0.7:
                    top_signals.append(signal)

            # 按信号强度排序，取前10个
            top_signals.sort(key=lambda x: x.signal_strength, reverse=True)
            top_signals = top_signals[:10]

            # 构建统计文本
            stats_text = ""
            for strategy, count in strategy_stats.items():
                stats_text += f"• **{strategy}**: {count}个信号\n"

            # 构建高强度信号列表
            top_signals_text = ""
            if top_signals:
                for i, signal in enumerate(top_signals, 1):
                    strength_emoji = "🔴" if signal.signal_strength >= 0.9 else "🟡" if signal.signal_strength >= 0.8 else "🟢"
                    # 添加更多信号详情
                    signal_detail = ""
                    if hasattr(signal, 'latest_close') and signal.latest_close > 0:
                        signal_detail += f" 价格:{signal.latest_close:.2f}"
                    if hasattr(signal, 'volume_ratio') and signal.volume_ratio > 0:
                        signal_detail += f" 量比:{signal.volume_ratio:.2f}"

                    top_signals_text += f"{i}. **{signal.stock_code}** {signal.stock_name} - {signal.strategy_name} {strength_emoji}{signal_detail}\n"
            else:
                top_signals_text = "暂无高强度信号"

            # 构建现代化卡片消息
            card = {
                "config": {
                    "wide_screen_mode": True
                },
                "header": {
                    "template": "blue",
                    "title": {
                        "content": f"📊 盘后策略选股报告 ({datetime.now().strftime('%Y-%m-%d')})",
                        "tag": "plain_text"
                    }
                },
                "elements": [
                    {
                        "tag": "div",
                        "text": {
                            "content": f"**📈 今日策略执行概况**\n\n共产生 **{len(signals)}** 个策略信号，涵盖 **{len(strategy_stats)}** 种策略类型。",
                            "tag": "lark_md"
                        }
                    },
                    {
                        "tag": "hr"
                    },
                    {
                        "tag": "div",
                        "text": {
                            "content": f"**📊 策略信号统计**\n\n{stats_text}",
                            "tag": "lark_md"
                        }
                    },
                    {
                        "tag": "hr"
                    },
                    {
                        "tag": "div",
                        "text": {
                            "content": f"**⭐ 高强度信号 (强度≥0.7)**\n\n{top_signals_text}",
                            "tag": "lark_md"
                        }
                    },
                    {
                        "tag": "hr"
                    },
                    {
                        "tag": "note",
                        "elements": [
                            {
                                "tag": "plain_text",
                                "content": f"QuantFM盘后策略系统 | 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
                            }
                        ]
                    }
                ]
            }

            return card

        except Exception as e:
            self.logger.error(f"构建策略汇总消息失败: {e}")
            # 返回简单消息作为备选
            return self._build_simple_strategy_message(signals)

    def _build_simple_strategy_message(self, signals: List) -> Dict:
        """构建简单策略消息（备选方案）"""
        return {
            "config": {"wide_screen_mode": True},
            "header": {
                "template": "blue",
                "title": {"content": "盘后策略选股报告", "tag": "plain_text"}
            },
            "elements": [
                {
                    "tag": "div",
                    "text": {
                        "content": f"今日共产生 {len(signals)} 个策略信号",
                        "tag": "plain_text"
                    }
                }
            ]
        }
    
    def _build_message(self, signal) -> Dict:
        """构建富媒体消息"""
        try:
            # 信号类型标识
            signal_emoji = "[火]" if signal.signal_type == "opening_anomaly" else "[涨]"

            # 价格变动方向
            if signal.price_change > 0:
                price_emoji = "[涨]"
                price_color = "green"
            elif signal.price_change < 0:
                price_emoji = "[跌]"
                price_color = "red"
            else:
                price_emoji = "[平]"
                price_color = "grey"
            
            # 信号强度
            if signal.confidence >= 0.8:
                confidence_emoji = "🔴"  # 强信号
            elif signal.confidence >= 0.6:
                confidence_emoji = "🟡"  # 中等信号
            else:
                confidence_emoji = "🟢"  # 弱信号
            
            # 构建卡片消息
            card = {
                "config": {
                    "wide_screen_mode": True
                },
                "header": {
                    "template": "red" if signal.signal_type == "opening_anomaly" else "blue",
                    "title": {
                        "content": f"{signal_emoji} 成交量异动提醒",
                        "tag": "plain_text"
                    }
                },
                "elements": [
                    {
                        "tag": "div",
                        "text": {
                            "content": f"**股票代码**: {signal.stock_code}\n**异动类型**: {'[火] 开盘异动' if signal.signal_type == 'opening_anomaly' else '[涨] 常规异动'}\n**成交量比值**: {signal.volume_ratio:.2f}倍\n**价格变动**: {price_emoji} {signal.price_change*100:+.2f}%\n**当前价格**: ¥{signal.current_price:.2f}\n**成交量**: {signal.volume:,}手\n**信号强度**: {confidence_emoji} {signal.confidence:.1%}\n**时间**: {signal.timestamp.strftime('%H:%M:%S')}",
                            "tag": "lark_md"
                        }
                    },
                    {
                        "tag": "hr"
                    },
                    {
                        "tag": "div",
                        "fields": [
                            {
                                "is_short": True,
                                "text": {
                                    "content": f"**异动级别**\n{self._get_anomaly_level(signal)}",
                                    "tag": "lark_md"
                                }
                            },
                            {
                                "is_short": True,
                                "text": {
                                    "content": f"**建议操作**\n{self._get_action_advice(signal)}",
                                    "tag": "lark_md"
                                }
                            }
                        ]
                    },
                    {
                        "tag": "hr"
                    },
                    {
                        "tag": "note",
                        "elements": [
                            {
                                "tag": "plain_text",
                                "content": f"QuantFM实时监控 | {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
                            }
                        ]
                    }
                ]
            }
            
            return card
            
        except Exception as e:
            self.logger.error(f"构建消息失败: {e}")
            # 返回简单消息作为备选
            return self._build_simple_message(signal)
    
    def _build_simple_message(self, signal) -> Dict:
        """构建简单消息（备选方案）"""
        return {
            "config": {"wide_screen_mode": True},
            "header": {
                "template": "blue",
                "title": {"content": "成交量异动", "tag": "plain_text"}
            },
            "elements": [
                {
                    "tag": "div",
                    "text": {
                        "content": f"{signal.stock_code} 成交量异动 {signal.volume_ratio:.2f}倍",
                        "tag": "plain_text"
                    }
                }
            ]
        }
    
    def _get_anomaly_level(self, signal) -> str:
        """获取异动级别"""
        if signal.volume_ratio >= 3.0:
            return "🔴 极强异动"
        elif signal.volume_ratio >= 2.5:
            return "🟠 强异动"
        elif signal.volume_ratio >= 2.0:
            return "🟡 中等异动"
        else:
            return "🟢 轻微异动"
    
    def _get_action_advice(self, signal) -> str:
        """获取操作建议"""
        if signal.signal_type == "opening_anomaly":
            if signal.price_change > 0:
                return "[火箭] 关注突破"
            else:
                return "[警告] 注意风险"
        else:
            if signal.volume_ratio >= 2.0:
                return "[眼睛] 密切关注"
            else:
                return "[图表] 持续观察"
    
    def send_card_message(self, card: Dict, stock_code: str = "") -> bool:
        """
        发送卡片消息（兼容方法）

        这个方法是为了兼容 IntradayStockMonitor 中的调用

        Args:
            card: 卡片消息内容
            stock_code: 股票代码（可选）

        Returns:
            bool: 发送是否成功
        """
        try:
            if not self.enabled:
                self.logger.debug("飞书通知器未启用，跳过发送")
                return False

            # 检查频率限制
            if not self._check_rate_limit():
                self.logger.warning("飞书通知频率限制，跳过发送")
                return False

            # 发送卡片消息
            result = self._send_message(card)

            if result:
                self.notification_count += 1
                self.logger.debug(f"成功发送卡片消息 {stock_code}")
            else:
                self.logger.error(f"发送卡片消息失败 {stock_code}")

            return result

        except Exception as e:
            self.logger.error(f"发送卡片消息异常: {e}")
            return False

    def send_volume_surge_alert(self, signal_data: Dict[str, Any]):
        """
        发送成交量激增信号通知

        Args:
            signal_data: 信号数据字典
        """
        try:
            if not self.enabled:
                return

            # 构造消息内容
            stock_code = signal_data.get('stock_code', '')
            stock_name = signal_data.get('stock_name', '')
            concepts = signal_data.get('concepts', '暂无概念')
            signal_type = signal_data.get('signal_type', '')
            surge_ratio = signal_data.get('surge_ratio', 0)
            current_volume = signal_data.get('current_volume', 0)
            historical_avg_volume = signal_data.get('historical_avg_volume', 0)
            confidence = signal_data.get('confidence', 0)
            continuous_count = signal_data.get('continuous_count', 0)
            timestamp = signal_data.get('timestamp')
            period_info = signal_data.get('period_info', '')
            current_price = signal_data.get('current_price', 0)
            change_percent = signal_data.get('change_percent', 0)
            signal_count = signal_data.get('signal_count', 1)

            # 确定信号类型和阈值
            if signal_type == 'OPENING':
                type_name = "开盘期激增"
                threshold = 100
                type_emoji = "🔥"
                template_color = "red"
            else:
                type_name = "盘中期激增"
                threshold = 10
                type_emoji = "⚡"
                template_color = "blue"

            # 计算激增级别
            surge_level = self._get_surge_level(surge_ratio, threshold)

            # 格式化涨跌幅显示
            change_sign = "+" if change_percent >= 0 else ""
            change_color = "🔴" if change_percent >= 0 else "🟢"

            # 构造紧凑的飞书卡片消息（两行显示）
            card_content = {
                "config": {
                    "wide_screen_mode": True
                },
                "elements": [
                    {
                        "tag": "div",
                        "text": {
                            "content": f"{type_emoji} **{stock_code} {stock_name}** | {concepts} | {surge_ratio:.1f}倍激增 | 第{signal_count}次信号",
                            "tag": "lark_md"
                        }
                    },
                    {
                        "tag": "div",
                        "text": {
                            "content": f"💰 现价: {current_price:.2f}元 | {change_color} 涨跌: {change_sign}{change_percent:.2f}% | ⏰ {timestamp.strftime('%H:%M:%S') if timestamp else ''}",
                            "tag": "lark_md"
                        }
                    }
                ]
            }

            # 发送消息 - 修复缺失的_send_card_message方法，使用现有的_send_message方法
            self._send_message(card_content)

        except Exception as e:
            self.logger.error(f"发送成交量激增信号通知失败: {e}")

    def _get_surge_level(self, surge_ratio: float, threshold: float) -> str:
        """获取激增级别"""
        ratio_multiple = surge_ratio / threshold

        if ratio_multiple >= 5.0:
            return "🔴 极强激增"
        elif ratio_multiple >= 3.0:
            return "🟠 强激增"
        elif ratio_multiple >= 2.0:
            return "🟡 中等激增"
        elif ratio_multiple >= 1.0:
            return "🟢 轻微激增"
        else:
            return "⚪ 未达阈值"

    def _get_continuity_status(self, continuous_count: int) -> str:
        """获取连续状态"""
        if continuous_count >= 5:
            return f"🔥 连续{continuous_count}次"
        elif continuous_count >= 3:
            return f"⚡ 连续{continuous_count}次"
        elif continuous_count >= 2:
            return f"🟡 连续{continuous_count}次"
        else:
            return f"🟢 第{continuous_count}次"

    def _generate_sign(self, timestamp: str) -> str:
        """
        生成飞书签名 - 根据成功案例修复算法

        关键发现：我们之前的算法完全错了！

        正确的算法（来自成功案例）：
        1. key = timestamp + "\n" + secret
        2. msg = "" (空字符串)
        3. hmac.new(key, msg, digestmod=sha256)

        错误的算法（我们之前的）：
        1. hmac.new(secret, timestamp + "\n" + secret, digestmod=sha256)

        参考成功案例：
        key = f'{timestamp}\n{secret}'
        key_enc = key.encode('utf-8')
        msg = ""
        msg_enc = msg.encode('utf-8')
        hmac_code = hmac.new(key_enc, msg_enc, digestmod=sha256).digest()
        """
        try:
            # 步骤1：构建密钥 - key = timestamp + "\n" + secret
            key = f'{timestamp}\n{self.secret}'
            key_enc = key.encode('utf-8')

            # 步骤2：待签名消息 - 空字符串
            msg = ""
            msg_enc = msg.encode('utf-8')

            # 步骤3：使用正确的HMAC算法
            hmac_code = hmac.new(key_enc, msg_enc, digestmod=hashlib.sha256).digest()

            # 步骤4：base64编码
            sign = base64.b64encode(hmac_code).decode('utf-8')

            # 详细调试信息
            self.logger.info(f"飞书签名生成（修复后的正确算法）:")
            self.logger.info(f"  timestamp: {timestamp}")
            self.logger.info(f"  secret: {self.secret}")
            self.logger.info(f"  key: {repr(key)}")
            self.logger.info(f"  msg: {repr(msg)}")
            self.logger.info(f"  hmac结果长度: {len(hmac_code)}")
            self.logger.info(f"  最终签名: {sign}")

            return sign

        except Exception as e:
            self.logger.error(f"生成飞书签名失败: {e}")
            import traceback
            self.logger.error(f"错误详情: {traceback.format_exc()}")
            return ""
    
    def test_connection(self) -> bool:
        """
        测试飞书连接 - 全面诊断

        基于大量测试发现：
        1. 所有签名算法（秒级、毫秒级时间戳）都失败
        2. 不使用签名也失败
        3. 不同消息格式都失败
        4. 所有测试都返回相同的19021错误

        结论：这个webhook URL或Secret可能已经失效
        """
        try:
            self.logger.info("🔍 开始飞书连接全面诊断...")

            # 测试1: 使用毫秒级时间戳的签名消息
            self.logger.info("📋 测试1: 毫秒级时间戳签名消息")
            result1 = self._send_text_message("QuantFM诊断测试1 - 毫秒级时间戳")

            # 测试2: 不使用签名的消息
            self.logger.info("📋 测试2: 不使用签名的消息")
            try:
                import requests
                data = {
                    'msg_type': 'text',
                    'content': {'text': 'QuantFM诊断测试2 - 无签名'}
                }
                response = requests.post(
                    self.webhook_url,
                    json=data,
                    headers={'Content-Type': 'application/json'},
                    timeout=10,
                    proxies={'http': '', 'https': ''}
                )
                result2 = response.status_code == 200 and response.json().get('code') == 0
                self.logger.info(f"无签名测试结果: {response.json()}")
            except Exception as e:
                result2 = False
                self.logger.error(f"无签名测试异常: {e}")

            # 诊断结论
            if not result1 and not result2:
                self.logger.error("❌ 飞书连接诊断结论:")
                self.logger.error("   1. 所有签名算法都失败")
                self.logger.error("   2. 不使用签名也失败")
                self.logger.error("   3. 建议检查webhook URL和Secret是否正确")
                self.logger.error("   4. 建议重新创建飞书机器人")
                return False
            else:
                self.logger.info("✅ 飞书连接测试成功")
                return True

        except Exception as e:
            self.logger.error(f"飞书连接测试异常: {e}")
            return False
