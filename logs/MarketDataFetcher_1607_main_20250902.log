﻿2025-09-02 11:20:28 [INFO] [MarketDataFetcher_1607_main] - 成功加载配置文件: config/main.toml
2025-09-02 11:20:28 [INFO] [MarketDataFetcher_1607_main] - 成功加载市场数据获取器配置文件: config/market_data_fetcher.toml
2025-09-02 11:20:28 [INFO] [MarketDataFetcher_1607_main] - [成功] MarketDataFetcher重构完成：专注于tick数据获取和数据库存储
2025-09-02 11:20:28 [INFO] [MarketDataFetcher_1607_main] - 计算得到最优线程数: 16 (CPU核心数: 8, 配置最大线程数: 100)
2025-09-02 11:20:28 [INFO] [MarketDataFetcher_1607_main] - 开始加载股票列表...
2025-09-02 11:20:28 [INFO] [MarketDataFetcher_1607_main] - 正在从数据库加载股票列表...
2025-09-02 11:20:28 [INFO] [MarketDataFetcher_1607_main] - [调试] 执行数据库查询: SELECT stock_code, stock_name FROM stock_info ORDER BY stock_code
2025-09-02 11:20:38 [INFO] [MarketDataFetcher_1607_main] - [调试] 数据库查询结果数量: 4396
2025-09-02 11:20:38 [INFO] [MarketDataFetcher_1607_main] - [成功] 从数据库成功获取 4396 只股票
2025-09-02 11:20:38 [INFO] [MarketDataFetcher_1607_main] - [数据库] 从PostgreSQL xystock数据库加载 4396 只股票
2025-09-02 11:20:38 [INFO] [MarketDataFetcher_1607_main] - 成功将 4396 只股票保存到配置文件: config/stock_list.toml
2025-09-02 11:20:38 [INFO] [MarketDataFetcher_1607_main] - [初始化] 股票列表加载完成: 4396 只股票
2025-09-02 11:20:38 [INFO] [MarketDataFetcher_1607_main] - [初始化] 有效股票代码: 4396 个
2025-09-02 11:20:38 [INFO] [MarketDataFetcher_1607_main] - [初始化] 前5个股票代码: ['000001', '000002', '000006', '000007', '000008']
2025-09-02 11:20:39 [INFO] [MarketDataFetcher_1607_main] - 创建了 88 个股票批次，每批最多 50 只股票
2025-09-02 11:20:39 [INFO] [MarketDataFetcher_1607_main] - 正在初始化数据库管理器...
2025-09-02 11:20:39 [INFO] [MarketDataFetcher_1607_main] - [成功] 数据库管理器初始化成功
2025-09-02 11:20:39 [INFO] [MarketDataFetcher_1607_main] - 正在清理可能存在的问题数据库连接...
2025-09-02 11:20:39 [INFO] [MarketDataFetcher_1607_main] - 没有发现问题连接
2025-09-02 11:20:39 [INFO] [MarketDataFetcher_1607_main] - 📦 使用数据库缓存机制
2025-09-02 11:20:39 [INFO] [MarketDataFetcher_1607_main] - 重置tick数据记录，清空 0 只股票的历史数据（包含成交量和价格）
2025-09-02 11:20:39 [INFO] [MarketDataFetcher_1607_main] - 开始加载股票列表...
2025-09-02 11:20:39 [INFO] [MarketDataFetcher_1607_main] - 正在从数据库加载股票列表...
2025-09-02 11:20:39 [INFO] [MarketDataFetcher_1607_main] - [调试] 执行数据库查询: SELECT stock_code, stock_name FROM stock_info ORDER BY stock_code
2025-09-02 11:20:39 [INFO] [MarketDataFetcher_1607_main] - [调试] 数据库查询结果数量: 4396
2025-09-02 11:20:39 [INFO] [MarketDataFetcher_1607_main] - [成功] 从数据库成功获取 4396 只股票
2025-09-02 11:20:39 [INFO] [MarketDataFetcher_1607_main] - [数据库] 从PostgreSQL xystock数据库加载 4396 只股票
2025-09-02 11:20:39 [INFO] [MarketDataFetcher_1607_main] - 成功将 4396 只股票保存到配置文件: config/stock_list.toml
2025-09-02 11:20:39 [INFO] [MarketDataFetcher_1607_main] - 定时任务线程启动
2025-09-02 11:20:41 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 4392/4392 条Tick数据
2025-09-02 11:20:41 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:20:41 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:20:41 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:20:41 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 4392 条记录，耗时: 0.14秒
2025-09-02 11:20:41 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:20:40, 股票数: 4396
2025-09-02 11:20:41 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1934.28 毫秒，每秒处理股票数: 2272.69
2025-09-02 11:20:41 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 1.93s (网络: 1.93s, 处理: -0.00s, 数据库: 1.93s)
2025-09-02 11:20:41 [WARNING] [MarketDataFetcher_1607_main] - [警告] 总耗时 1.93s 接近或超过获取间隔 2s，可能无法保证每 2s 获取一次数据
2025-09-02 11:20:41 [WARNING] [MarketDataFetcher_1607_main] - [修复] 建议：考虑增加获取间隔或优化网络连接
2025-09-02 11:20:42 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 1357/1357 条Tick数据
2025-09-02 11:20:42 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:20:42 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:20:42 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:20:42 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 1357 条记录，耗时: 0.87秒
2025-09-02 11:20:42 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:20:41, 股票数: 4396
2025-09-02 11:20:42 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1463.39 毫秒，每秒处理股票数: 3003.97
2025-09-02 11:20:42 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 1.46s (网络: 1.46s, 处理: -0.00s, 数据库: 1.46s)
2025-09-02 11:20:43 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 377/377 条Tick数据
2025-09-02 11:20:43 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:20:43 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:20:43 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:20:43 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 377 条记录，耗时: 0.12秒
2025-09-02 11:20:43 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:20:43, 股票数: 4396
2025-09-02 11:20:43 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 659.20 毫秒，每秒处理股票数: 6668.67
2025-09-02 11:20:43 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 0.66s (网络: 0.66s, 处理: 0.00s, 数据库: 0.66s)
2025-09-02 11:20:44 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 1975/1975 条Tick数据
2025-09-02 11:20:44 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:20:44 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:20:44 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:20:44 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 1975 条记录，耗时: 0.41秒
2025-09-02 11:20:44 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:20:44, 股票数: 4396
2025-09-02 11:20:44 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 944.12 毫秒，每秒处理股票数: 4656.19
2025-09-02 11:20:44 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 0.94s (网络: 0.94s, 处理: -0.00s, 数据库: 0.94s)
2025-09-02 11:20:46 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 1386/1386 条Tick数据
2025-09-02 11:20:46 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:20:46 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:20:46 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:20:46 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 1386 条记录，耗时: 0.26秒
2025-09-02 11:20:46 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:20:46, 股票数: 4396
2025-09-02 11:20:46 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 965.21 毫秒，每秒处理股票数: 4554.43
2025-09-02 11:20:46 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 0.97s (网络: 0.97s, 处理: 0.00s, 数据库: 0.97s)
2025-09-02 11:20:48 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 1579/1579 条Tick数据
2025-09-02 11:20:48 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:20:48 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:20:48 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:20:48 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 1579 条记录，耗时: 0.09秒
2025-09-02 11:20:48 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:20:48, 股票数: 4396
2025-09-02 11:20:48 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 725.72 毫秒，每秒处理股票数: 6057.47
2025-09-02 11:20:48 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 0.73s (网络: 0.73s, 处理: -0.00s, 数据库: 0.73s)
2025-09-02 11:20:51 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 2036/2036 条Tick数据
2025-09-02 11:20:51 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:20:51 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:20:51 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:20:51 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 2036 条记录，耗时: 0.52秒
2025-09-02 11:20:51 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:20:50, 股票数: 4396
2025-09-02 11:20:51 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1068.06 毫秒，每秒处理股票数: 4115.86
2025-09-02 11:20:51 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 1.07s (网络: 1.07s, 处理: 0.00s, 数据库: 1.07s)
2025-09-02 11:20:52 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 1436/1436 条Tick数据
2025-09-02 11:20:52 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:20:52 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:20:52 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:20:52 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 1436 条记录，耗时: 0.10秒
2025-09-02 11:20:52 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:20:52, 股票数: 4396
2025-09-02 11:20:52 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 624.53 毫秒，每秒处理股票数: 7038.91
2025-09-02 11:20:52 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 0.62s (网络: 0.62s, 处理: -0.00s, 数据库: 0.62s)
2025-09-02 11:20:55 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 1152/1152 条Tick数据
2025-09-02 11:20:55 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:20:55 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:20:55 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:20:55 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 1152 条记录，耗时: 0.38秒
2025-09-02 11:20:55 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:20:54, 股票数: 4396
2025-09-02 11:20:55 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1043.90 毫秒，每秒处理股票数: 4211.14
2025-09-02 11:20:55 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 1.04s (网络: 1.04s, 处理: -0.00s, 数据库: 1.04s)
2025-09-02 11:20:57 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 2154/2154 条Tick数据
2025-09-02 11:20:57 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:20:57 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:20:57 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:20:57 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 2154 条记录，耗时: 0.37秒
2025-09-02 11:20:57 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:20:56, 股票数: 4396
2025-09-02 11:20:57 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1038.11 毫秒，每秒处理股票数: 4234.62
2025-09-02 11:20:57 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 1.04s (网络: 1.04s, 处理: -0.00s, 数据库: 1.04s)
2025-09-02 11:20:58 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 1531/1531 条Tick数据
2025-09-02 11:20:58 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:20:58 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:20:58 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:20:58 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 1531 条记录，耗时: 0.30秒
2025-09-02 11:20:58 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:20:58, 股票数: 4396
2025-09-02 11:20:58 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 995.41 毫秒，每秒处理股票数: 4416.26
2025-09-02 11:20:58 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 1.00s (网络: 1.00s, 处理: -0.00s, 数据库: 1.00s)
2025-09-02 11:21:01 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 1410/1410 条Tick数据
2025-09-02 11:21:01 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:21:01 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:21:01 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:21:01 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 1410 条记录，耗时: 0.27秒
2025-09-02 11:21:01 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:21:00, 股票数: 4396
2025-09-02 11:21:01 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1001.04 毫秒，每秒处理股票数: 4391.45
2025-09-02 11:21:01 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 1.00s (网络: 1.00s, 处理: -0.00s, 数据库: 1.00s)
2025-09-02 11:21:02 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 2444/2444 条Tick数据
2025-09-02 11:21:02 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:21:02 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:21:02 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:21:02 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 2444 条记录，耗时: 0.10秒
2025-09-02 11:21:02 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:21:02, 股票数: 4396
2025-09-02 11:21:02 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 756.49 毫秒，每秒处理股票数: 5811.07
2025-09-02 11:21:02 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 0.76s (网络: 0.76s, 处理: -0.00s, 数据库: 0.76s)
2025-09-02 11:21:05 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 1604/1604 条Tick数据
2025-09-02 11:21:05 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:21:05 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:21:05 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:21:05 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 1604 条记录，耗时: 0.61秒
2025-09-02 11:21:05 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:21:04, 股票数: 4396
2025-09-02 11:21:05 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1190.51 毫秒，每秒处理股票数: 3692.55
2025-09-02 11:21:05 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 1.19s (网络: 1.19s, 处理: 0.00s, 数据库: 1.19s)
2025-09-02 11:21:06 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 1377/1377 条Tick数据
2025-09-02 11:21:06 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:21:06 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:21:06 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:21:06 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 1377 条记录，耗时: 0.11秒
2025-09-02 11:21:06 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:21:06, 股票数: 4396
2025-09-02 11:21:06 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 647.30 毫秒，每秒处理股票数: 6791.32
2025-09-02 11:21:06 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 0.65s (网络: 0.65s, 处理: -0.00s, 数据库: 0.65s)
2025-09-02 11:21:09 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 2297/2297 条Tick数据
2025-09-02 11:21:09 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:21:09 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:21:09 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:21:09 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 2297 条记录，耗时: 0.56秒
2025-09-02 11:21:09 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:21:08, 股票数: 4396
2025-09-02 11:21:09 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1129.47 毫秒，每秒处理股票数: 3892.07
2025-09-02 11:21:09 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 1.13s (网络: 1.13s, 处理: -0.00s, 数据库: 1.13s)
2025-09-02 11:21:11 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 1855/1855 条Tick数据
2025-09-02 11:21:11 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:21:11 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:21:11 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:21:11 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 1855 条记录，耗时: 0.31秒
2025-09-02 11:21:11 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:21:10, 股票数: 4396
2025-09-02 11:21:11 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1054.05 毫秒，每秒处理股票数: 4170.58
2025-09-02 11:21:11 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 1.05s (网络: 1.05s, 处理: 0.00s, 数据库: 1.05s)
2025-09-02 11:21:12 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 1044/1044 条Tick数据
2025-09-02 11:21:12 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:21:12 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:21:12 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:21:12 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 1044 条记录，耗时: 0.08秒
2025-09-02 11:21:12 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:21:12, 股票数: 4396
2025-09-02 11:21:12 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 796.94 毫秒，每秒处理股票数: 5516.12
2025-09-02 11:21:12 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 0.80s (网络: 0.80s, 处理: -0.00s, 数据库: 0.80s)
2025-09-02 11:21:15 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 2306/2306 条Tick数据
2025-09-02 11:21:15 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:21:15 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:21:15 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:21:15 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 2306 条记录，耗时: 0.49秒
2025-09-02 11:21:15 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:21:14, 股票数: 4396
2025-09-02 11:21:15 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1266.58 毫秒，每秒处理股票数: 3470.76
2025-09-02 11:21:15 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 1.27s (网络: 1.27s, 处理: -0.00s, 数据库: 1.27s)
2025-09-02 11:21:16 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 1433/1433 条Tick数据
2025-09-02 11:21:16 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:21:16 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:21:16 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:21:16 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 1433 条记录，耗时: 0.09秒
2025-09-02 11:21:16 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:21:16, 股票数: 4396
2025-09-02 11:21:16 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 785.03 毫秒，每秒处理股票数: 5599.80
2025-09-02 11:21:16 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 0.79s (网络: 0.79s, 处理: 0.00s, 数据库: 0.79s)
2025-09-02 11:21:19 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 1305/1305 条Tick数据
2025-09-02 11:21:19 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:21:19 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:21:19 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:21:19 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 1305 条记录，耗时: 0.44秒
2025-09-02 11:21:19 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:21:18, 股票数: 4396
2025-09-02 11:21:19 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1058.22 毫秒，每秒处理股票数: 4154.16
2025-09-02 11:21:19 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 1.06s (网络: 1.06s, 处理: -0.00s, 数据库: 1.06s)
2025-09-02 11:21:20 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 2049/2049 条Tick数据
2025-09-02 11:21:20 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:21:20 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:21:20 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:21:20 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 2049 条记录，耗时: 0.11秒
2025-09-02 11:21:20 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:21:20, 股票数: 4396
2025-09-02 11:21:20 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 656.41 毫秒，每秒处理股票数: 6697.00
2025-09-02 11:21:20 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 0.66s (网络: 0.66s, 处理: -0.00s, 数据库: 0.66s)
2025-09-02 11:21:23 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 1696/1696 条Tick数据
2025-09-02 11:21:23 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:21:23 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:21:23 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:21:23 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 1696 条记录，耗时: 0.54秒
2025-09-02 11:21:23 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:21:22, 股票数: 4396
2025-09-02 11:21:23 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1056.67 毫秒，每秒处理股票数: 4160.22
2025-09-02 11:21:23 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 1.06s (网络: 1.06s, 处理: -0.00s, 数据库: 1.06s)
2025-09-02 11:21:24 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 1129/1129 条Tick数据
2025-09-02 11:21:24 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:21:24 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:21:24 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:21:24 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 1129 条记录，耗时: 0.21秒
2025-09-02 11:21:24 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:21:24, 股票数: 4396
2025-09-02 11:21:24 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 818.20 毫秒，每秒处理股票数: 5372.74
2025-09-02 11:21:24 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 0.82s (网络: 0.82s, 处理: -0.00s, 数据库: 0.82s)
2025-09-02 11:21:26 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 2295/2295 条Tick数据
2025-09-02 11:21:26 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:21:26 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:21:26 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:21:26 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 2295 条记录，耗时: 0.38秒
2025-09-02 11:21:26 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:21:26, 股票数: 4396
2025-09-02 11:21:26 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 975.55 毫秒，每秒处理股票数: 4506.19
2025-09-02 11:21:26 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 0.98s (网络: 0.98s, 处理: -0.00s, 数据库: 0.98s)
2025-09-02 11:21:29 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 1574/1574 条Tick数据
2025-09-02 11:21:29 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:21:29 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:21:29 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:21:29 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 1574 条记录，耗时: 0.27秒
2025-09-02 11:21:29 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:21:28, 股票数: 4396
2025-09-02 11:21:29 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1032.43 毫秒，每秒处理股票数: 4257.93
2025-09-02 11:21:29 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 1.03s (网络: 1.03s, 处理: -0.00s, 数据库: 1.03s)
2025-09-02 11:21:30 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 1288/1288 条Tick数据
2025-09-02 11:21:30 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:21:30 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:21:30 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:21:30 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 1288 条记录，耗时: 0.09秒
2025-09-02 11:21:30 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:21:30, 股票数: 4396
2025-09-02 11:21:30 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 813.75 毫秒，每秒处理股票数: 5402.13
2025-09-02 11:21:30 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 0.81s (网络: 0.81s, 处理: -0.00s, 数据库: 0.81s)
2025-09-02 11:21:33 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 2208/2208 条Tick数据
2025-09-02 11:21:33 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:21:33 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:21:33 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:21:33 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 2208 条记录，耗时: 0.54秒
2025-09-02 11:21:33 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:21:32, 股票数: 4396
2025-09-02 11:21:33 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1195.09 毫秒，每秒处理股票数: 3678.38
2025-09-02 11:21:33 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 1.20s (网络: 1.20s, 处理: 0.00s, 数据库: 1.20s)
2025-09-02 11:21:34 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 1351/1351 条Tick数据
2025-09-02 11:21:34 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:21:34 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:21:34 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:21:34 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 1351 条记录，耗时: 0.09秒
2025-09-02 11:21:34 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:21:34, 股票数: 4396
2025-09-02 11:21:34 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 677.20 毫秒，每秒处理股票数: 6491.40
2025-09-02 11:21:34 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 0.68s (网络: 0.68s, 处理: 0.00s, 数据库: 0.68s)
2025-09-02 11:21:36 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 1307/1307 条Tick数据
2025-09-02 11:21:36 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:21:36 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:21:36 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:21:36 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 1307 条记录，耗时: 0.43秒
2025-09-02 11:21:36 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:21:36, 股票数: 4396
2025-09-02 11:21:36 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 957.41 毫秒，每秒处理股票数: 4591.54
2025-09-02 11:21:36 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 0.96s (网络: 0.96s, 处理: -0.00s, 数据库: 0.96s)
2025-09-02 11:21:38 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 2057/2057 条Tick数据
2025-09-02 11:21:38 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:21:38 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:21:38 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:21:38 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 2057 条记录，耗时: 0.33秒
2025-09-02 11:21:38 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:21:38, 股票数: 4396
2025-09-02 11:21:38 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 884.51 毫秒，每秒处理股票数: 4969.97
2025-09-02 11:21:38 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 0.88s (网络: 0.88s, 处理: 0.00s, 数据库: 0.88s)
2025-09-02 11:21:41 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 1705/1705 条Tick数据
2025-09-02 11:21:41 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:21:41 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:21:41 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:21:41 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 1705 条记录，耗时: 0.29秒
2025-09-02 11:21:41 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:21:40, 股票数: 4396
2025-09-02 11:21:41 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1030.35 毫秒，每秒处理股票数: 4266.52
2025-09-02 11:21:41 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 1.03s (网络: 1.03s, 处理: -0.00s, 数据库: 1.03s)
2025-09-02 11:21:42 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 981/981 条Tick数据
2025-09-02 11:21:42 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:21:42 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:21:42 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:21:42 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 981 条记录，耗时: 0.08秒
2025-09-02 11:21:42 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:21:42, 股票数: 4396
2025-09-02 11:21:42 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 800.29 毫秒，每秒处理股票数: 5493.00
2025-09-02 11:21:42 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 0.80s (网络: 0.80s, 处理: -0.00s, 数据库: 0.80s)
2025-09-02 11:21:45 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 2071/2071 条Tick数据
2025-09-02 11:21:45 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:21:45 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:21:45 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:21:45 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 2071 条记录，耗时: 0.45秒
2025-09-02 11:21:45 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:21:44, 股票数: 4396
2025-09-02 11:21:45 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1215.40 毫秒，每秒处理股票数: 3616.92
2025-09-02 11:21:45 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 1.22s (网络: 1.22s, 处理: -0.00s, 数据库: 1.22s)
2025-09-02 11:21:46 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 1364/1364 条Tick数据
2025-09-02 11:21:46 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:21:46 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:21:46 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:21:46 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 1364 条记录，耗时: 0.09秒
2025-09-02 11:21:46 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:21:46, 股票数: 4396
2025-09-02 11:21:46 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 817.27 毫秒，每秒处理股票数: 5378.91
2025-09-02 11:21:46 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 0.82s (网络: 0.82s, 处理: 0.00s, 数据库: 0.82s)
2025-09-02 11:21:49 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 1197/1197 条Tick数据
2025-09-02 11:21:49 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:21:49 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:21:49 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:21:49 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 1197 条记录，耗时: 0.41秒
2025-09-02 11:21:49 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:21:48, 股票数: 4396
2025-09-02 11:21:49 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1046.20 毫秒，每秒处理股票数: 4201.85
2025-09-02 11:21:49 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 1.05s (网络: 1.05s, 处理: -0.00s, 数据库: 1.05s)
2025-09-02 11:21:50 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 2060/2060 条Tick数据
2025-09-02 11:21:50 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:21:50 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:21:50 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:21:50 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 2060 条记录，耗时: 0.10秒
2025-09-02 11:21:50 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:21:50, 股票数: 4396
2025-09-02 11:21:50 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 682.45 毫秒，每秒处理股票数: 6441.46
2025-09-02 11:21:50 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 0.68s (网络: 0.68s, 处理: 0.00s, 数据库: 0.68s)
2025-09-02 11:21:53 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 1423/1423 条Tick数据
2025-09-02 11:21:53 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:21:53 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:21:53 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:21:53 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 1423 条记录，耗时: 0.52秒
2025-09-02 11:21:53 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:21:52, 股票数: 4396
2025-09-02 11:21:53 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1029.32 毫秒，每秒处理股票数: 4270.78
2025-09-02 11:21:53 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 1.03s (网络: 1.03s, 处理: -0.00s, 数据库: 1.03s)
2025-09-02 11:21:54 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 1173/1173 条Tick数据
2025-09-02 11:21:54 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:21:54 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:21:54 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:21:54 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 1173 条记录，耗时: 0.22秒
2025-09-02 11:21:54 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:21:54, 股票数: 4396
2025-09-02 11:21:54 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 813.43 毫秒，每秒处理股票数: 5404.30
2025-09-02 11:21:54 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 0.81s (网络: 0.81s, 处理: 0.00s, 数据库: 0.81s)
2025-09-02 11:21:57 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 2094/2094 条Tick数据
2025-09-02 11:21:57 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:21:57 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:21:57 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:21:57 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 2094 条记录，耗时: 0.37秒
2025-09-02 11:21:57 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:21:56, 股票数: 4396
2025-09-02 11:21:57 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1045.78 毫秒，每秒处理股票数: 4203.54
2025-09-02 11:21:57 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 1.05s (网络: 1.05s, 处理: 0.00s, 数据库: 1.05s)
2025-09-02 11:21:59 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 1689/1689 条Tick数据
2025-09-02 11:21:59 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:21:59 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:21:59 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:21:59 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 1689 条记录，耗时: 0.32秒
2025-09-02 11:21:59 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:21:58, 股票数: 4396
2025-09-02 11:21:59 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1029.32 毫秒，每秒处理股票数: 4270.79
2025-09-02 11:21:59 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 1.03s (网络: 1.03s, 处理: -0.00s, 数据库: 1.03s)
2025-09-02 11:22:00 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 1049/1049 条Tick数据
2025-09-02 11:22:00 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:22:00 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:22:00 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:22:00 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 1049 条记录，耗时: 0.09秒
2025-09-02 11:22:00 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:22:00, 股票数: 4396
2025-09-02 11:22:00 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 829.71 毫秒，每秒处理股票数: 5298.26
2025-09-02 11:22:00 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 0.83s (网络: 0.83s, 处理: -0.00s, 数据库: 0.83s)
2025-09-02 11:22:03 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 2325/2325 条Tick数据
2025-09-02 11:22:03 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:22:03 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:22:03 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:22:03 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 2325 条记录，耗时: 0.52秒
2025-09-02 11:22:03 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:22:02, 股票数: 4396
2025-09-02 11:22:03 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1228.61 毫秒，每秒处理股票数: 3578.01
2025-09-02 11:22:03 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 1.23s (网络: 1.23s, 处理: -0.00s, 数据库: 1.23s)
2025-09-02 11:22:04 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 1476/1476 条Tick数据
2025-09-02 11:22:04 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:22:04 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:22:04 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:22:04 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 1476 条记录，耗时: 0.10秒
2025-09-02 11:22:04 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:22:04, 股票数: 4396
2025-09-02 11:22:04 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 709.51 毫秒，每秒处理股票数: 6195.85
2025-09-02 11:22:04 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 0.71s (网络: 0.71s, 处理: -0.00s, 数据库: 0.71s)
2025-09-02 11:22:07 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 1455/1455 条Tick数据
2025-09-02 11:22:07 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:22:07 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:22:07 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:22:07 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 1455 条记录，耗时: 0.46秒
2025-09-02 11:22:07 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:22:06, 股票数: 4396
2025-09-02 11:22:07 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1008.79 毫秒，每秒处理股票数: 4357.70
2025-09-02 11:22:07 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 1.01s (网络: 1.01s, 处理: 0.00s, 数据库: 1.01s)
2025-09-02 11:22:08 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 2195/2195 条Tick数据
2025-09-02 11:22:08 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:22:08 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:22:08 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:22:08 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 2195 条记录，耗时: 0.36秒
2025-09-02 11:22:08 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:22:08, 股票数: 4396
2025-09-02 11:22:08 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 902.26 毫秒，每秒处理股票数: 4872.20
2025-09-02 11:22:08 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 0.90s (网络: 0.90s, 处理: -0.00s, 数据库: 0.90s)
2025-09-02 11:22:10 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 1739/1739 条Tick数据
2025-09-02 11:22:10 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:22:10 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:22:10 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:22:10 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 1739 条记录，耗时: 0.30秒
2025-09-02 11:22:10 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:22:10, 股票数: 4396
2025-09-02 11:22:10 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 929.97 毫秒，每秒处理股票数: 4727.03
2025-09-02 11:22:10 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 0.93s (网络: 0.93s, 处理: -0.00s, 数据库: 0.93s)
2025-09-02 11:22:12 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 1049/1049 条Tick数据
2025-09-02 11:22:12 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:22:12 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:22:12 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:22:12 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 1049 条记录，耗时: 0.21秒
2025-09-02 11:22:12 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:22:12, 股票数: 4396
2025-09-02 11:22:12 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 941.28 毫秒，每秒处理股票数: 4670.24
2025-09-02 11:22:12 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 0.94s (网络: 0.94s, 处理: -0.00s, 数据库: 0.94s)
2025-09-02 11:22:15 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 2255/2255 条Tick数据
2025-09-02 11:22:15 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:22:15 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:22:15 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:22:15 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 2255 条记录，耗时: 0.38秒
2025-09-02 11:22:15 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:22:14, 股票数: 4396
2025-09-02 11:22:15 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1151.97 毫秒，每秒处理股票数: 3816.08
2025-09-02 11:22:15 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 1.15s (网络: 1.15s, 处理: -0.00s, 数据库: 1.15s)
2025-09-02 11:22:16 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 1522/1522 条Tick数据
2025-09-02 11:22:16 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:22:16 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:22:16 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:22:16 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 1522 条记录，耗时: 0.10秒
2025-09-02 11:22:16 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:22:16, 股票数: 4396
2025-09-02 11:22:16 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 821.14 毫秒，每秒处理股票数: 5353.52
2025-09-02 11:22:16 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 0.82s (网络: 0.82s, 处理: -0.00s, 数据库: 0.82s)
2025-09-02 11:22:19 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 1258/1258 条Tick数据
2025-09-02 11:22:19 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:22:19 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:22:19 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:22:19 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 1258 条记录，耗时: 0.43秒
2025-09-02 11:22:19 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:22:18, 股票数: 4396
2025-09-02 11:22:19 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1080.63 毫秒，每秒处理股票数: 4067.98
2025-09-02 11:22:19 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 1.08s (网络: 1.08s, 处理: 0.00s, 数据库: 1.08s)
2025-09-02 11:22:20 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 2260/2260 条Tick数据
2025-09-02 11:22:20 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:22:20 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:22:20 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:22:20 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 2260 条记录，耗时: 0.11秒
2025-09-02 11:22:20 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:22:20, 股票数: 4396
2025-09-02 11:22:20 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 687.65 毫秒，每秒处理股票数: 6392.83
2025-09-02 11:22:20 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 0.69s (网络: 0.69s, 处理: -0.00s, 数据库: 0.69s)
2025-09-02 11:22:23 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 1698/1698 条Tick数据
2025-09-02 11:22:23 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:22:23 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:22:23 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:22:23 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 1698 条记录，耗时: 0.59秒
2025-09-02 11:22:23 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:22:22, 股票数: 4396
2025-09-02 11:22:23 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1098.12 毫秒，每秒处理股票数: 4003.22
2025-09-02 11:22:23 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 1.10s (网络: 1.10s, 处理: -0.00s, 数据库: 1.10s)
2025-09-02 11:22:24 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 1350/1350 条Tick数据
2025-09-02 11:22:24 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:22:24 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:22:24 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:22:24 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 1350 条记录，耗时: 0.24秒
2025-09-02 11:22:24 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:22:24, 股票数: 4396
2025-09-02 11:22:24 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 796.98 毫秒，每秒处理股票数: 5515.82
2025-09-02 11:22:24 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 0.80s (网络: 0.80s, 处理: -0.00s, 数据库: 0.80s)
2025-09-02 11:22:27 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 2571/2571 条Tick数据
2025-09-02 11:22:27 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:22:27 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:22:27 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:22:27 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 2571 条记录，耗时: 0.47秒
2025-09-02 11:22:27 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:22:26, 股票数: 4396
2025-09-02 11:22:27 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1139.19 毫秒，每秒处理股票数: 3858.89
2025-09-02 11:22:27 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 1.14s (网络: 1.14s, 处理: -0.00s, 数据库: 1.14s)
2025-09-02 11:22:28 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 1847/1847 条Tick数据
2025-09-02 11:22:28 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:22:28 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:22:28 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:22:28 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 1847 条记录，耗时: 0.13秒
2025-09-02 11:22:28 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:22:28, 股票数: 4396
2025-09-02 11:22:28 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 743.60 毫秒，每秒处理股票数: 5911.80
2025-09-02 11:22:28 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 0.74s (网络: 0.74s, 处理: -0.00s, 数据库: 0.74s)
2025-09-02 11:22:31 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 1280/1280 条Tick数据
2025-09-02 11:22:31 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:22:31 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:22:31 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:22:31 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 1280 条记录，耗时: 0.45秒
2025-09-02 11:22:31 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:22:30, 股票数: 4396
2025-09-02 11:22:31 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1113.19 毫秒，每秒处理股票数: 3949.00
2025-09-02 11:22:31 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 1.11s (网络: 1.11s, 处理: -0.00s, 数据库: 1.11s)
2025-09-02 11:22:33 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 2465/2465 条Tick数据
2025-09-02 11:22:33 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:22:33 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:22:33 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:22:33 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 2465 条记录，耗时: 0.41秒
2025-09-02 11:22:33 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:22:32, 股票数: 4396
2025-09-02 11:22:33 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1150.75 毫秒，每秒处理股票数: 3820.12
2025-09-02 11:22:33 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 1.15s (网络: 1.15s, 处理: -0.00s, 数据库: 1.15s)
2025-09-02 11:22:34 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 1649/1649 条Tick数据
2025-09-02 11:22:34 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:22:34 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:22:34 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:22:34 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 1649 条记录，耗时: 0.13秒
2025-09-02 11:22:34 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:22:34, 股票数: 4396
2025-09-02 11:22:34 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 719.56 毫秒，每秒处理股票数: 6109.29
2025-09-02 11:22:34 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 0.72s (网络: 0.72s, 处理: -0.00s, 数据库: 0.72s)
2025-09-02 11:22:37 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 1116/1116 条Tick数据
2025-09-02 11:22:37 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:22:37 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:22:37 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:22:37 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 1116 条记录，耗时: 0.44秒
2025-09-02 11:22:37 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:22:36, 股票数: 4396
2025-09-02 11:22:37 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1108.36 毫秒，每秒处理股票数: 3966.21
2025-09-02 11:22:37 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 1.11s (网络: 1.11s, 处理: -0.00s, 数据库: 1.11s)
2025-09-02 11:22:38 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 2203/2203 条Tick数据
2025-09-02 11:22:38 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:22:38 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:22:38 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:22:38 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 2203 条记录，耗时: 0.10秒
2025-09-02 11:22:38 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:22:38, 股票数: 4396
2025-09-02 11:22:38 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 698.16 毫秒，每秒处理股票数: 6296.53
2025-09-02 11:22:38 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 0.70s (网络: 0.70s, 处理: -0.00s, 数据库: 0.70s)
2025-09-02 11:22:41 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 1624/1624 条Tick数据
2025-09-02 11:22:41 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:22:41 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:22:41 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:22:41 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 1624 条记录，耗时: 0.57秒
2025-09-02 11:22:41 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:22:40, 股票数: 4396
2025-09-02 11:22:41 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1098.24 毫秒，每秒处理股票数: 4002.77
2025-09-02 11:22:41 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 1.10s (网络: 1.10s, 处理: 0.00s, 数据库: 1.10s)
2025-09-02 11:22:42 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 994/994 条Tick数据
2025-09-02 11:22:42 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:22:42 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:22:42 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:22:42 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 994 条记录，耗时: 0.21秒
2025-09-02 11:22:42 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:22:42, 股票数: 4396
2025-09-02 11:22:42 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 778.86 毫秒，每秒处理股票数: 5644.17
2025-09-02 11:22:42 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 0.78s (网络: 0.78s, 处理: 0.00s, 数据库: 0.78s)
2025-09-02 11:22:44 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 2062/2062 条Tick数据
2025-09-02 11:22:44 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:22:44 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:22:44 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:22:44 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 2062 条记录，耗时: 0.33秒
2025-09-02 11:22:44 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:22:44, 股票数: 4396
2025-09-02 11:22:44 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 993.26 毫秒，每秒处理股票数: 4425.84
2025-09-02 11:22:44 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 0.99s (网络: 0.99s, 处理: -0.00s, 数据库: 0.99s)
2025-09-02 11:22:47 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 1698/1698 条Tick数据
2025-09-02 11:22:47 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:22:47 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:22:47 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:22:47 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 1698 条记录，耗时: 0.30秒
2025-09-02 11:22:47 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:22:46, 股票数: 4396
2025-09-02 11:22:47 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1066.80 毫秒，每秒处理股票数: 4120.73
2025-09-02 11:22:47 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 1.07s (网络: 1.07s, 处理: -0.00s, 数据库: 1.07s)
2025-09-02 11:22:48 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 1003/1003 条Tick数据
2025-09-02 11:22:48 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:22:48 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:22:48 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:22:48 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 1003 条记录，耗时: 0.09秒
2025-09-02 11:22:48 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:22:48, 股票数: 4396
2025-09-02 11:22:48 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 852.48 毫秒，每秒处理股票数: 5156.75
2025-09-02 11:22:48 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 0.85s (网络: 0.85s, 处理: -0.00s, 数据库: 0.85s)
2025-09-02 11:22:51 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 2124/2124 条Tick数据
2025-09-02 11:22:51 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:22:51 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:22:51 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:22:51 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 2124 条记录，耗时: 0.48秒
2025-09-02 11:22:51 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:22:50, 股票数: 4396
2025-09-02 11:22:51 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1219.27 毫秒，每秒处理股票数: 3605.43
2025-09-02 11:22:51 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 1.22s (网络: 1.22s, 处理: -0.00s, 数据库: 1.22s)
2025-09-02 11:22:52 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 1406/1406 条Tick数据
2025-09-02 11:22:52 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:22:52 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:22:52 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:22:52 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 1406 条记录，耗时: 0.10秒
2025-09-02 11:22:52 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:22:52, 股票数: 4396
2025-09-02 11:22:52 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 758.22 毫秒，每秒处理股票数: 5797.82
2025-09-02 11:22:52 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 0.76s (网络: 0.76s, 处理: -0.00s, 数据库: 0.76s)
2025-09-02 11:22:55 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 1173/1173 条Tick数据
2025-09-02 11:22:55 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:22:55 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:22:55 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:22:55 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 1173 条记录，耗时: 0.43秒
2025-09-02 11:22:55 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:22:54, 股票数: 4396
2025-09-02 11:22:55 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1036.55 毫秒，每秒处理股票数: 4240.98
2025-09-02 11:22:55 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 1.04s (网络: 1.04s, 处理: -0.00s, 数据库: 1.04s)
2025-09-02 11:22:56 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 2195/2195 条Tick数据
2025-09-02 11:22:56 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:22:56 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:22:56 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:22:56 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 2195 条记录，耗时: 0.11秒
2025-09-02 11:22:56 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:22:56, 股票数: 4396
2025-09-02 11:22:56 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 663.30 毫秒，每秒处理股票数: 6627.46
2025-09-02 11:22:56 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 0.66s (网络: 0.66s, 处理: -0.00s, 数据库: 0.66s)
2025-09-02 11:22:59 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 1705/1705 条Tick数据
2025-09-02 11:22:59 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:22:59 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:22:59 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:22:59 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 1705 条记录，耗时: 0.61秒
2025-09-02 11:22:59 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:22:58, 股票数: 4396
2025-09-02 11:22:59 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1146.13 毫秒，每秒处理股票数: 3835.51
2025-09-02 11:22:59 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 1.15s (网络: 1.15s, 处理: -0.00s, 数据库: 1.15s)
2025-09-02 11:23:00 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 1077/1077 条Tick数据
2025-09-02 11:23:00 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:23:00 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:23:00 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:23:00 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 1077 条记录，耗时: 0.22秒
2025-09-02 11:23:00 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:23:00, 股票数: 4396
2025-09-02 11:23:00 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 854.65 毫秒，每秒处理股票数: 5143.65
2025-09-02 11:23:00 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 0.85s (网络: 0.85s, 处理: -0.00s, 数据库: 0.85s)
2025-09-02 11:23:03 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 2315/2315 条Tick数据
2025-09-02 11:23:03 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:23:03 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:23:03 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:23:03 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 2315 条记录，耗时: 0.37秒
2025-09-02 11:23:03 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:23:02, 股票数: 4396
2025-09-02 11:23:03 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1085.78 毫秒，每秒处理股票数: 4048.71
2025-09-02 11:23:03 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 1.09s (网络: 1.09s, 处理: -0.00s, 数据库: 1.09s)
2025-09-02 11:23:05 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 1948/1948 条Tick数据
2025-09-02 11:23:05 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:23:05 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:23:05 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:23:05 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 1948 条记录，耗时: 0.35秒
2025-09-02 11:23:05 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:23:04, 股票数: 4396
2025-09-02 11:23:05 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1118.13 毫秒，每秒处理股票数: 3931.56
2025-09-02 11:23:05 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 1.12s (网络: 1.12s, 处理: -0.00s, 数据库: 1.12s)
2025-09-02 11:23:06 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 1211/1211 条Tick数据
2025-09-02 11:23:06 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:23:06 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:23:06 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:23:06 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 1211 条记录，耗时: 0.09秒
2025-09-02 11:23:06 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:23:06, 股票数: 4396
2025-09-02 11:23:06 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 820.11 毫秒，每秒处理股票数: 5360.23
2025-09-02 11:23:06 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 0.82s (网络: 0.82s, 处理: -0.00s, 数据库: 0.82s)
2025-09-02 11:23:09 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 2783/2783 条Tick数据
2025-09-02 11:23:09 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:23:09 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:23:09 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:23:09 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 2783 条记录，耗时: 0.61秒
2025-09-02 11:23:09 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:23:08, 股票数: 4396
2025-09-02 11:23:09 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1288.09 毫秒，每秒处理股票数: 3412.80
2025-09-02 11:23:09 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 1.29s (网络: 1.29s, 处理: -0.00s, 数据库: 1.29s)
2025-09-02 11:23:10 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 1715/1715 条Tick数据
2025-09-02 11:23:10 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:23:10 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:23:10 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:23:10 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 1715 条记录，耗时: 0.10秒
2025-09-02 11:23:10 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:23:10, 股票数: 4396
2025-09-02 11:23:10 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 688.17 毫秒，每秒处理股票数: 6387.93
2025-09-02 11:23:10 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 0.69s (网络: 0.69s, 处理: -0.00s, 数据库: 0.69s)
2025-09-02 11:23:13 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 1532/1532 条Tick数据
2025-09-02 11:23:13 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:23:13 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:23:13 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:23:13 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 1532 条记录，耗时: 0.52秒
2025-09-02 11:23:13 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:23:12, 股票数: 4396
2025-09-02 11:23:13 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1047.97 毫秒，每秒处理股票数: 4194.79
2025-09-02 11:23:13 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 1.05s (网络: 1.05s, 处理: -0.00s, 数据库: 1.05s)
2025-09-02 11:23:14 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 2344/2344 条Tick数据
2025-09-02 11:23:14 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:23:14 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:23:14 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:23:14 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 2344 条记录，耗时: 0.37秒
2025-09-02 11:23:14 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:23:14, 股票数: 4396
2025-09-02 11:23:14 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 950.70 毫秒，每秒处理股票数: 4623.96
2025-09-02 11:23:14 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 0.95s (网络: 0.95s, 处理: -0.00s, 数据库: 0.95s)
2025-09-02 11:23:17 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 1844/1844 条Tick数据
2025-09-02 11:23:17 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:23:17 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:23:17 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:23:17 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 1844 条记录，耗时: 0.33秒
2025-09-02 11:23:17 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:23:16, 股票数: 4396
2025-09-02 11:23:17 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1044.79 毫秒，每秒处理股票数: 4207.53
2025-09-02 11:23:17 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 1.04s (网络: 1.04s, 处理: 0.00s, 数据库: 1.04s)
2025-09-02 11:23:18 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 1088/1088 条Tick数据
2025-09-02 11:23:18 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:23:18 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:23:18 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:23:18 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 1088 条记录，耗时: 0.22秒
2025-09-02 11:23:18 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:23:18, 股票数: 4396
2025-09-02 11:23:18 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 974.30 毫秒，每秒处理股票数: 4511.97
2025-09-02 11:23:18 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 0.97s (网络: 0.97s, 处理: -0.00s, 数据库: 0.97s)
2025-09-02 11:23:21 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 2394/2394 条Tick数据
2025-09-02 11:23:21 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:23:21 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:23:21 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:23:21 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 2394 条记录，耗时: 0.40秒
2025-09-02 11:23:21 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:23:20, 股票数: 4396
2025-09-02 11:23:21 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1164.32 毫秒，每秒处理股票数: 3775.60
2025-09-02 11:23:21 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 1.16s (网络: 1.16s, 处理: -0.00s, 数据库: 1.16s)
2025-09-02 11:23:22 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 1762/1762 条Tick数据
2025-09-02 11:23:22 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:23:22 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:23:22 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:23:22 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 1762 条记录，耗时: 0.10秒
2025-09-02 11:23:22 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:23:22, 股票数: 4396
2025-09-02 11:23:22 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 762.31 毫秒，每秒处理股票数: 5766.68
2025-09-02 11:23:22 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 0.76s (网络: 0.76s, 处理: 0.00s, 数据库: 0.76s)
2025-09-02 11:23:25 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 981/981 条Tick数据
2025-09-02 11:23:25 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:23:25 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:23:25 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:23:25 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 981 条记录，耗时: 0.42秒
2025-09-02 11:23:25 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:23:24, 股票数: 4396
2025-09-02 11:23:25 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1041.54 毫秒，每秒处理股票数: 4220.66
2025-09-02 11:23:25 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 1.04s (网络: 1.04s, 处理: -0.00s, 数据库: 1.04s)
2025-09-02 11:23:26 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 2397/2397 条Tick数据
2025-09-02 11:23:26 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:23:26 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:23:26 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:23:26 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 2397 条记录，耗时: 0.12秒
2025-09-02 11:23:26 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:23:26, 股票数: 4396
2025-09-02 11:23:26 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 708.86 毫秒，每秒处理股票数: 6201.50
2025-09-02 11:23:26 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 0.71s (网络: 0.71s, 处理: -0.00s, 数据库: 0.71s)
2025-09-02 11:23:29 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 1747/1747 条Tick数据
2025-09-02 11:23:29 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:23:29 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:23:29 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:23:29 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 1747 条记录，耗时: 0.66秒
2025-09-02 11:23:29 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:23:28, 股票数: 4396
2025-09-02 11:23:29 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1192.37 毫秒，每秒处理股票数: 3686.77
2025-09-02 11:23:29 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 1.19s (网络: 1.19s, 处理: -0.00s, 数据库: 1.19s)
2025-09-02 11:23:30 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 1049/1049 条Tick数据
2025-09-02 11:23:30 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:23:30 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:23:30 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:23:30 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 1049 条记录，耗时: 0.10秒
2025-09-02 11:23:30 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:23:30, 股票数: 4396
2025-09-02 11:23:30 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 644.26 毫秒，每秒处理股票数: 6823.38
2025-09-02 11:23:30 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 0.64s (网络: 0.64s, 处理: -0.00s, 数据库: 0.64s)
2025-09-02 11:23:33 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 2270/2270 条Tick数据
2025-09-02 11:23:33 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:23:33 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:23:33 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:23:33 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 2270 条记录，耗时: 0.50秒
2025-09-02 11:23:33 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:23:32, 股票数: 4396
2025-09-02 11:23:33 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1132.02 毫秒，每秒处理股票数: 3883.31
2025-09-02 11:23:33 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 1.13s (网络: 1.13s, 处理: -0.00s, 数据库: 1.13s)
2025-09-02 11:23:34 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 1939/1939 条Tick数据
2025-09-02 11:23:34 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:23:34 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:23:34 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:23:34 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 1939 条记录，耗时: 0.33秒
2025-09-02 11:23:34 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:23:34, 股票数: 4396
2025-09-02 11:23:34 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 970.58 毫秒，每秒处理股票数: 4529.27
2025-09-02 11:23:34 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 0.97s (网络: 0.97s, 处理: -0.00s, 数据库: 0.97s)
2025-09-02 11:23:36 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 1238/1238 条Tick数据
2025-09-02 11:23:36 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:23:36 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:23:36 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:23:36 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 1238 条记录，耗时: 0.23秒
2025-09-02 11:23:36 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:23:36, 股票数: 4396
2025-09-02 11:23:36 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 947.30 毫秒，每秒处理股票数: 4640.55
2025-09-02 11:23:36 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 0.95s (网络: 0.95s, 处理: 0.00s, 数据库: 0.95s)
2025-09-02 11:23:39 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 2118/2118 条Tick数据
2025-09-02 11:23:39 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:23:39 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:23:39 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:23:39 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 2118 条记录，耗时: 0.37秒
2025-09-02 11:23:39 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:23:38, 股票数: 4396
2025-09-02 11:23:39 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1092.18 毫秒，每秒处理股票数: 4024.98
2025-09-02 11:23:39 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 1.09s (网络: 1.09s, 处理: -0.00s, 数据库: 1.09s)
2025-09-02 11:23:40 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 1760/1760 条Tick数据
2025-09-02 11:23:40 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:23:40 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:23:40 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:23:40 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 1760 条记录，耗时: 0.10秒
2025-09-02 11:23:40 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:23:40, 股票数: 4396
2025-09-02 11:23:40 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 747.50 毫秒，每秒处理股票数: 5880.91
2025-09-02 11:23:40 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 0.75s (网络: 0.75s, 处理: -0.00s, 数据库: 0.75s)
2025-09-02 11:23:43 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 964/964 条Tick数据
2025-09-02 11:23:43 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:23:43 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:23:43 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:23:43 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 964 条记录，耗时: 0.44秒
2025-09-02 11:23:43 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:23:42, 股票数: 4396
2025-09-02 11:23:43 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1046.52 毫秒，每秒处理股票数: 4200.58
2025-09-02 11:23:43 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 1.05s (网络: 1.05s, 处理: -0.00s, 数据库: 1.05s)
2025-09-02 11:23:44 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 2097/2097 条Tick数据
2025-09-02 11:23:44 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:23:44 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:23:44 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:23:44 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 2097 条记录，耗时: 0.12秒
2025-09-02 11:23:44 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:23:44, 股票数: 4396
2025-09-02 11:23:44 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 690.38 毫秒，每秒处理股票数: 6367.51
2025-09-02 11:23:44 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 0.69s (网络: 0.69s, 处理: -0.00s, 数据库: 0.69s)
2025-09-02 11:23:47 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 1568/1568 条Tick数据
2025-09-02 11:23:47 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:23:47 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:23:47 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:23:47 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 1568 条记录，耗时: 0.54秒
2025-09-02 11:23:47 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:23:46, 股票数: 4396
2025-09-02 11:23:47 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1073.05 毫秒，每秒处理股票数: 4096.74
2025-09-02 11:23:47 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 1.07s (网络: 1.07s, 处理: -0.00s, 数据库: 1.07s)
2025-09-02 11:23:48 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 1075/1075 条Tick数据
2025-09-02 11:23:48 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:23:48 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:23:48 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:23:48 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 1075 条记录，耗时: 0.25秒
2025-09-02 11:23:48 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:23:48, 股票数: 4396
2025-09-02 11:23:48 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 831.74 毫秒，每秒处理股票数: 5285.29
2025-09-02 11:23:48 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 0.83s (网络: 0.83s, 处理: -0.00s, 数据库: 0.83s)
2025-09-02 11:23:51 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 2137/2137 条Tick数据
2025-09-02 11:23:51 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:23:51 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:23:51 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:23:51 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 2137 条记录，耗时: 0.36秒
2025-09-02 11:23:51 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:23:50, 股票数: 4396
2025-09-02 11:23:51 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1013.58 毫秒，每秒处理股票数: 4337.12
2025-09-02 11:23:51 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 1.01s (网络: 1.01s, 处理: -0.00s, 数据库: 1.01s)
2025-09-02 11:23:52 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 1401/1401 条Tick数据
2025-09-02 11:23:52 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:23:52 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:23:52 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:23:52 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 1401 条记录，耗时: 0.26秒
2025-09-02 11:23:52 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:23:52, 股票数: 4396
2025-09-02 11:23:52 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 988.17 毫秒，每秒处理股票数: 4448.62
2025-09-02 11:23:52 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 0.99s (网络: 0.99s, 处理: -0.00s, 数据库: 0.99s)
2025-09-02 11:23:54 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 1328/1328 条Tick数据
2025-09-02 11:23:54 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:23:54 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:23:54 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:23:54 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 1328 条记录，耗时: 0.25秒
2025-09-02 11:23:54 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:23:54, 股票数: 4396
2025-09-02 11:23:54 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 995.27 毫秒，每秒处理股票数: 4416.91
2025-09-02 11:23:54 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 1.00s (网络: 1.00s, 处理: -0.00s, 数据库: 1.00s)
2025-09-02 11:23:56 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 2192/2192 条Tick数据
2025-09-02 11:23:56 [INFO] [MarketDataFetcher_1607_main] - 插入统计: 成功率=100.0%, 平均速度=8338条/秒, 总记录数=170181
2025-09-02 11:23:56 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:23:56 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:23:56 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:23:56 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 2192 条记录，耗时: 0.10秒
2025-09-02 11:23:56 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:23:56, 股票数: 4396
2025-09-02 11:23:56 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 792.61 毫秒，每秒处理股票数: 5546.25
2025-09-02 11:23:56 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 0.79s (网络: 0.79s, 处理: -0.00s, 数据库: 0.79s)
2025-09-02 11:23:59 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 1527/1527 条Tick数据
2025-09-02 11:23:59 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:23:59 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:23:59 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:23:59 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 1527 条记录，耗时: 0.64秒
2025-09-02 11:23:59 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:23:58, 股票数: 4396
2025-09-02 11:23:59 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1273.35 毫秒，每秒处理股票数: 3452.30
2025-09-02 11:23:59 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 1.27s (网络: 1.27s, 处理: -0.00s, 数据库: 1.27s)
2025-09-02 11:24:00 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 1297/1297 条Tick数据
2025-09-02 11:24:00 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:24:00 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:24:00 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:24:00 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 1297 条记录，耗时: 0.09秒
2025-09-02 11:24:00 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:24:00, 股票数: 4396
2025-09-02 11:24:00 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 656.00 毫秒，每秒处理股票数: 6701.21
2025-09-02 11:24:00 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 0.66s (网络: 0.66s, 处理: 0.00s, 数据库: 0.66s)
2025-09-02 11:24:03 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 2268/2268 条Tick数据
2025-09-02 11:24:03 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:24:03 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:24:03 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:24:03 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 2268 条记录，耗时: 0.55秒
2025-09-02 11:24:03 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:24:02, 股票数: 4396
2025-09-02 11:24:03 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1084.01 毫秒，每秒处理股票数: 4055.33
2025-09-02 11:24:03 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 1.08s (网络: 1.08s, 处理: 0.00s, 数据库: 1.08s)
2025-09-02 11:24:04 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 1935/1935 条Tick数据
2025-09-02 11:24:04 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:24:04 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:24:04 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:24:04 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 1935 条记录，耗时: 0.32秒
2025-09-02 11:24:04 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:24:04, 股票数: 4396
2025-09-02 11:24:04 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 925.53 毫秒，每秒处理股票数: 4749.69
2025-09-02 11:24:04 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 0.93s (网络: 0.93s, 处理: -0.00s, 数据库: 0.93s)
2025-09-02 11:24:06 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 1188/1188 条Tick数据
2025-09-02 11:24:06 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:24:06 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:24:06 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:24:06 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 1188 条记录，耗时: 0.23秒
2025-09-02 11:24:06 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:24:06, 股票数: 4396
2025-09-02 11:24:06 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 933.71 毫秒，每秒处理股票数: 4708.08
2025-09-02 11:24:06 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 0.93s (网络: 0.93s, 处理: -0.00s, 数据库: 0.93s)
2025-09-02 11:24:09 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 2469/2469 条Tick数据
2025-09-02 11:24:09 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:24:09 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:24:09 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:24:09 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 2469 条记录，耗时: 0.38秒
2025-09-02 11:24:09 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:24:08, 股票数: 4396
2025-09-02 11:24:09 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1100.54 毫秒，每秒处理股票数: 3994.42
2025-09-02 11:24:09 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 1.10s (网络: 1.10s, 处理: -0.00s, 数据库: 1.10s)
2025-09-02 11:24:11 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 1853/1853 条Tick数据
2025-09-02 11:24:11 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:24:11 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:24:11 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:24:11 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 1853 条记录，耗时: 0.32秒
2025-09-02 11:24:11 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:24:10, 股票数: 4396
2025-09-02 11:24:11 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1069.84 毫秒，每秒处理股票数: 4109.03
2025-09-02 11:24:11 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 1.07s (网络: 1.07s, 处理: -0.00s, 数据库: 1.07s)
2025-09-02 11:24:12 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 1523/1523 条Tick数据
2025-09-02 11:24:12 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:24:12 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:24:12 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:24:12 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 1523 条记录，耗时: 0.09秒
2025-09-02 11:24:12 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:24:12, 股票数: 4396
2025-09-02 11:24:12 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 798.07 毫秒，每秒处理股票数: 5508.29
2025-09-02 11:24:12 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 0.80s (网络: 0.80s, 处理: -0.00s, 数据库: 0.80s)
2025-09-02 11:24:15 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 2453/2453 条Tick数据
2025-09-02 11:24:15 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:24:15 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:24:15 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:24:15 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 2453 条记录，耗时: 0.62秒
2025-09-02 11:24:15 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:24:14, 股票数: 4396
2025-09-02 11:24:15 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1272.72 毫秒，每秒处理股票数: 3454.03
2025-09-02 11:24:15 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 1.27s (网络: 1.27s, 处理: 0.00s, 数据库: 1.27s)
2025-09-02 11:24:16 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 1493/1493 条Tick数据
2025-09-02 11:24:16 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:24:16 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:24:16 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:24:16 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 1493 条记录，耗时: 0.09秒
2025-09-02 11:24:16 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:24:16, 股票数: 4396
2025-09-02 11:24:16 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 680.64 毫秒，每秒处理股票数: 6458.59
2025-09-02 11:24:16 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 0.68s (网络: 0.68s, 处理: -0.00s, 数据库: 0.68s)
2025-09-02 11:24:18 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 1384/1384 条Tick数据
2025-09-02 11:24:18 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:24:18 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:24:18 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:24:18 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 1384 条记录，耗时: 0.47秒
2025-09-02 11:24:18 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:24:18, 股票数: 4396
2025-09-02 11:24:18 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 981.83 毫秒，每秒处理股票数: 4477.33
2025-09-02 11:24:18 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 0.98s (网络: 0.98s, 处理: 0.00s, 数据库: 0.98s)
2025-09-02 11:24:20 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 2405/2405 条Tick数据
2025-09-02 11:24:20 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:24:20 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:24:20 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:24:20 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 2405 条记录，耗时: 0.38秒
2025-09-02 11:24:20 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:24:20, 股票数: 4396
2025-09-02 11:24:20 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 964.75 毫秒，每秒处理股票数: 4556.62
2025-09-02 11:24:20 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 0.96s (网络: 0.96s, 处理: -0.00s, 数据库: 0.96s)
2025-09-02 11:24:23 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 2008/2008 条Tick数据
2025-09-02 11:24:23 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:24:23 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:24:23 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:24:23 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 2008 条记录，耗时: 0.34秒
2025-09-02 11:24:23 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:24:22, 股票数: 4396
2025-09-02 11:24:23 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1073.59 毫秒，每秒处理股票数: 4094.67
2025-09-02 11:24:23 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 1.07s (网络: 1.07s, 处理: -0.00s, 数据库: 1.07s)
2025-09-02 11:24:24 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 1105/1105 条Tick数据
2025-09-02 11:24:24 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:24:24 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:24:24 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:24:24 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 1105 条记录，耗时: 0.09秒
2025-09-02 11:24:24 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:24:24, 股票数: 4396
2025-09-02 11:24:24 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 821.81 毫秒，每秒处理股票数: 5349.18
2025-09-02 11:24:24 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 0.82s (网络: 0.82s, 处理: -0.00s, 数据库: 0.82s)
2025-09-02 11:24:27 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 2380/2380 条Tick数据
2025-09-02 11:24:27 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:24:27 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:24:27 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:24:27 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 2380 条记录，耗时: 0.52秒
2025-09-02 11:24:27 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:24:26, 股票数: 4396
2025-09-02 11:24:27 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1250.27 毫秒，每秒处理股票数: 3516.05
2025-09-02 11:24:27 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 1.25s (网络: 1.25s, 处理: -0.00s, 数据库: 1.25s)
2025-09-02 11:24:28 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 1477/1477 条Tick数据
2025-09-02 11:24:28 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:24:28 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:24:28 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:24:28 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 1477 条记录，耗时: 0.11秒
2025-09-02 11:24:28 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:24:28, 股票数: 4396
2025-09-02 11:24:28 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 828.07 毫秒，每秒处理股票数: 5308.73
2025-09-02 11:24:28 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 0.83s (网络: 0.83s, 处理: -0.00s, 数据库: 0.83s)
2025-09-02 11:24:31 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 1314/1314 条Tick数据
2025-09-02 11:24:31 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:24:31 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:24:31 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:24:31 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 1314 条记录，耗时: 0.44秒
2025-09-02 11:24:31 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:24:30, 股票数: 4396
2025-09-02 11:24:31 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1093.72 毫秒，每秒处理股票数: 4019.30
2025-09-02 11:24:31 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 1.09s (网络: 1.09s, 处理: 0.00s, 数据库: 1.09s)
2025-09-02 11:24:32 [INFO] [MarketDataFetcher_1607_main] - [成功] 成功保存 2244/2244 条Tick数据
2025-09-02 11:24:32 [INFO] [MarketDataFetcher_1607_main] - [启动] 性能优化报告:
2025-09-02 11:24:32 [INFO] [MarketDataFetcher_1607_main] -    缓存: 大小=0, 命中率=0.0%
2025-09-02 11:24:32 [INFO] [MarketDataFetcher_1607_main] -    批处理: 缓冲区=0, 阈值=5000
2025-09-02 11:24:32 [INFO] [MarketDataFetcher_1607_main] - 保存Tick数据完成，共 2244 条记录，耗时: 0.10秒
2025-09-02 11:24:32 [INFO] [MarketDataFetcher_1607_main] - 处理完成，时间: 11:24:32, 股票数: 4396
2025-09-02 11:24:32 [INFO] [MarketDataFetcher_1607_main] - 获取实时行情数据: 成功获取 4396 只股票的行情，成功批次: 88，失败批次: 0，耗时: 687.26 毫秒，每秒处理股票数: 6396.38
2025-09-02 11:24:32 [INFO] [MarketDataFetcher_1607_main] - [目标] 本次获取和保存数据总耗时: 0.69s (网络: 0.69s, 处理: -0.00s, 数据库: 0.69s)
